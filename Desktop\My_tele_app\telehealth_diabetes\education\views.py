from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.utils import timezone
from .models import EducationContent, EducationCategory, Recipe, ExerciseRoutine

def education_home(request):
    """Education home page"""
    categories = EducationCategory.objects.filter(is_active=True)
    featured_content = EducationContent.objects.filter(is_published=True, is_featured=True)[:6]

    return render(request, 'education/home.html', {
        'categories': categories,
        'featured_content': featured_content
    })

def content_library(request):
    """Education content library"""
    content_list = EducationContent.objects.filter(is_published=True).order_by('-created_at')
    categories = EducationCategory.objects.filter(is_active=True)

    # Filter by category if specified
    category_id = request.GET.get('category')
    if category_id:
        content_list = content_list.filter(category_id=category_id)

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        content_list = content_list.filter(title__icontains=search_query)

    paginator = Paginator(content_list, 12)
    page_number = request.GET.get('page')
    content = paginator.get_page(page_number)

    return render(request, 'education/library.html', {
        'content': content,
        'categories': categories,
        'search_query': search_query,
        'selected_category': category_id
    })

def content_detail(request, slug):
    """Education content detail"""
    content = get_object_or_404(EducationContent, slug=slug, is_published=True)
    content.increment_view_count()

    # Track progress if user is logged in
    progress = None
    if request.user.is_authenticated:
        try:
            from .models import PatientProgress
            patient_profile = request.user.patientprofile
            progress, created = PatientProgress.objects.get_or_create(
                patient=patient_profile,
                content=content
            )
            if created:
                progress.started_at = timezone.now()
                progress.save()
        except:
            pass

    return render(request, 'education/content_detail.html', {
        'content': content,
        'progress': progress
    })

def recipe_list(request):
    """Recipe list"""
    recipes = Recipe.objects.filter(is_published=True).order_by('-created_at')

    # Filter by meal type
    meal_type = request.GET.get('meal_type')
    if meal_type:
        recipes = recipes.filter(meal_type=meal_type)

    paginator = Paginator(recipes, 12)
    page_number = request.GET.get('page')
    recipes_page = paginator.get_page(page_number)

    return render(request, 'education/recipes.html', {
        'recipes': recipes_page,
        'selected_meal_type': meal_type
    })

def recipe_detail(request, slug):
    """Recipe detail"""
    recipe = get_object_or_404(Recipe, slug=slug, is_published=True)

    return render(request, 'education/recipe_detail.html', {
        'recipe': recipe
    })

def exercise_list(request):
    """Exercise list"""
    exercises = ExerciseRoutine.objects.filter(is_published=True).order_by('-created_at')

    # Filter by intensity
    intensity = request.GET.get('intensity')
    if intensity:
        exercises = exercises.filter(intensity_level=intensity)

    paginator = Paginator(exercises, 12)
    page_number = request.GET.get('page')
    exercises_page = paginator.get_page(page_number)

    return render(request, 'education/exercises.html', {
        'exercises': exercises_page,
        'selected_intensity': intensity
    })

def exercise_detail(request, slug):
    """Exercise detail"""
    exercise = get_object_or_404(ExerciseRoutine, slug=slug, is_published=True)

    return render(request, 'education/exercise_detail.html', {
        'exercise': exercise
    })
