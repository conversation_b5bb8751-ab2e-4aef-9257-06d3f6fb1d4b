{% extends 'base.html' %}
{% load static %}

{% block title %}Support Groups - Telehealth Diabetes Care{% endblock %}

{% block content %}
<div class="container py-5">
    <!-- Hero Section -->
    <div class="row mb-5">
        <div class="col-12 text-center">
            <h1 class="display-4 text-primary mb-4">
                <i class="fas fa-users me-3"></i>
                Support Groups
            </h1>
            <p class="lead">Connect with others who understand your diabetes journey</p>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="input-group">
                <input type="text" class="form-control" placeholder="Search support groups...">
                <button class="btn btn-primary" type="button">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
        <div class="col-md-4">
            <select class="form-select">
                <option value="">All Group Types</option>
                <option value="general">General Diabetes Support</option>
                <option value="type1">Type 1 Diabetes</option>
                <option value="type2">Type 2 Diabetes</option>
                <option value="newly_diagnosed">Newly Diagnosed</option>
                <option value="teens">Teen Support</option>
                <option value="parents">Parents of Diabetic Children</option>
            </select>
        </div>
    </div>

    <!-- Create Group Button -->
    {% if user.is_authenticated %}
    <div class="row mb-4">
        <div class="col-12">
            <a href="{% url 'support_groups:create' %}" class="btn btn-success">
                <i class="fas fa-plus me-2"></i>
                Create New Group
            </a>
        </div>
    </div>
    {% endif %}

    <!-- Support Groups List -->
    <div class="row">
        {% if groups %}
            {% for group in groups %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">{{ group.name }}</h5>
                        <small>{{ group.get_group_type_display }}</small>
                    </div>
                    <div class="card-body">
                        <p class="card-text">{{ group.description|truncatewords:20 }}</p>
                        
                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="fas fa-users me-1"></i>{{ group.member_count }} members
                                {% if group.location %}
                                    <br><i class="fas fa-map-marker-alt me-1"></i>{{ group.location }}
                                {% endif %}
                            </small>
                        </div>

                        <div class="mb-3">
                            {% if group.is_public %}
                                <span class="badge bg-success">Public</span>
                            {% else %}
                                <span class="badge bg-warning">Private</span>
                            {% endif %}
                            
                            {% if group.requires_approval %}
                                <span class="badge bg-info">Approval Required</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-footer bg-transparent border-0">
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'support_groups:detail' group.id %}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>View Details
                            </a>
                            {% if user.is_authenticated %}
                                <form method="post" action="{% url 'support_groups:join' group.id %}" class="d-inline">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-primary btn-sm">
                                        <i class="fas fa-user-plus me-1"></i>Join
                                    </button>
                                </form>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-users fa-5x text-muted mb-4"></i>
                    <h3 class="text-muted">No Support Groups Found</h3>
                    <p class="text-muted">Be the first to create a support group for your community!</p>
                    {% if user.is_authenticated %}
                        <a href="{% url 'support_groups:create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            Create First Group
                        </a>
                    {% endif %}
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Pagination -->
    {% if groups.has_other_pages %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="Support groups pagination">
                <ul class="pagination justify-content-center">
                    {% if groups.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ groups.previous_page_number }}">Previous</a>
                        </li>
                    {% endif %}
                    
                    {% for num in groups.paginator.page_range %}
                        {% if groups.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% else %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if groups.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ groups.next_page_number }}">Next</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}

    <!-- Benefits Section -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-0 shadow-lg bg-light">
                <div class="card-body p-5">
                    <h2 class="text-center text-primary mb-4">Why Join a Support Group?</h2>
                    <div class="row">
                        <div class="col-md-4 text-center mb-4">
                            <i class="fas fa-heart fa-3x text-danger mb-3"></i>
                            <h5>Emotional Support</h5>
                            <p>Connect with others who truly understand your challenges and celebrate your victories.</p>
                        </div>
                        <div class="col-md-4 text-center mb-4">
                            <i class="fas fa-lightbulb fa-3x text-warning mb-3"></i>
                            <h5>Practical Tips</h5>
                            <p>Learn from real experiences and discover new strategies for managing your diabetes.</p>
                        </div>
                        <div class="col-md-4 text-center mb-4">
                            <i class="fas fa-users fa-3x text-success mb-3"></i>
                            <h5>Community</h5>
                            <p>Build lasting friendships with people who share similar health journeys and goals.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
