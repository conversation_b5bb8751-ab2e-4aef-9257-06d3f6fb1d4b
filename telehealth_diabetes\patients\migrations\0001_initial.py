# Generated by Django 5.2.3 on 2025-06-30 15:20

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PatientProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('gender', models.CharField(blank=True, choices=[('M', 'Male'), ('F', 'Female'), ('O', 'Other')], max_length=1)),
                ('phone_number', models.CharField(blank=True, max_length=15)),
                ('address', models.TextField(blank=True)),
                ('emergency_contact_name', models.CharField(blank=True, max_length=100)),
                ('emergency_contact_phone', models.CharField(blank=True, max_length=15)),
                ('diabetes_type', models.CharField(blank=True, choices=[('type1', 'Type 1 Diabetes'), ('type2', 'Type 2 Diabetes'), ('gestational', 'Gestational Diabetes'), ('other', 'Other')], max_length=20)),
                ('diagnosis_date', models.DateField(blank=True, null=True)),
                ('hba1c_target', models.FloatField(blank=True, help_text='Target HbA1c percentage', null=True, validators=[django.core.validators.MinValueValidator(4.0), django.core.validators.MaxValueValidator(15.0)])),
                ('blood_glucose_target_min', models.FloatField(blank=True, help_text='Target minimum blood glucose (mmol/L)', null=True, validators=[django.core.validators.MinValueValidator(3.0), django.core.validators.MaxValueValidator(30.0)])),
                ('blood_glucose_target_max', models.FloatField(blank=True, help_text='Target maximum blood glucose (mmol/L)', null=True, validators=[django.core.validators.MinValueValidator(3.0), django.core.validators.MaxValueValidator(30.0)])),
                ('primary_doctor', models.CharField(blank=True, max_length=100)),
                ('healthcare_facility', models.CharField(blank=True, max_length=200)),
                ('preferred_language', models.CharField(default='en', max_length=20)),
                ('notification_preferences', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='PatientNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note', models.TextField()),
                ('is_private', models.BooleanField(default=True, help_text='Private notes only visible to providers')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='patient_notes', to=settings.AUTH_USER_MODEL)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notes', to='patients.patientprofile')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
