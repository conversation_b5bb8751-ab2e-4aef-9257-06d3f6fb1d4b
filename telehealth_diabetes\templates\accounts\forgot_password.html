{% extends 'base.html' %}
{% load static %}

{% block title %}Forgot Password - Telehealth Diabetes Care{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-key me-2"></i>
                        Forgot Password
                    </h4>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <div class="text-center mb-4">
                        <i class="fas fa-lock fa-3x text-warning mb-3"></i>
                        <p class="text-muted">
                            Enter your email address and we'll send you instructions to reset your password.
                        </p>
                    </div>
                    
                    <form method="post" id="forgotPasswordForm">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   required autofocus placeholder="Enter your email address">
                            <div class="form-text">
                                We'll send password reset instructions to this email.
                            </div>
                        </div>
                        
                        <!-- Loading indicator -->
                        <div id="loadingIndicator" class="text-center mb-3" style="display: none;">
                            <div class="spinner-border spinner-border-sm text-warning" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <span class="ms-2">Sending reset instructions...</span>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-warning" id="resetBtn">
                                <i class="fas fa-paper-plane me-2"></i>
                                Send Reset Instructions
                            </button>
                        </div>
                    </form>
                    
                    <hr>
                    
                    <div class="text-center">
                        <p class="mb-2">Remember your password?</p>
                        <a href="{% url 'accounts:login' %}" class="btn btn-outline-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            Back to Login
                        </a>
                    </div>
                    
                    <div class="text-center mt-3">
                        <small class="text-muted">
                            Don't have an account? 
                            <a href="{% url 'accounts:register' %}" class="text-decoration-none">Register here</a>
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- Help Section -->
            <div class="card mt-4 border-info">
                <div class="card-body">
                    <h6 class="card-title text-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Need Help?
                    </h6>
                    <div class="small text-muted">
                        <p class="mb-2">
                            <strong>Can't access your email?</strong><br>
                            Contact our support team for assistance.
                        </p>
                        <p class="mb-2">
                            <strong>Didn't receive the email?</strong><br>
                            Check your spam folder or try again in a few minutes.
                        </p>
                        <p class="mb-0">
                            <strong>Still having trouble?</strong><br>
                            Email us at: <a href="mailto:<EMAIL>"><EMAIL></a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-control {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
}

.form-control:focus {
    border-color: #ffc107;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.card {
    border: none;
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('forgotPasswordForm');
    const btn = document.getElementById('resetBtn');
    const loading = document.getElementById('loadingIndicator');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            // Validate email
            const email = document.getElementById('email').value.trim();
            if (!email) {
                alert('Please enter your email address.');
                e.preventDefault();
                return;
            }
            
            // Basic email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                alert('Please enter a valid email address.');
                e.preventDefault();
                return;
            }
            
            // Show loading state
            if (btn) {
                btn.disabled = true;
                btn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Sending...';
            }
            
            if (loading) {
                loading.style.display = 'block';
            }
            
            // Re-enable after timeout (safety)
            setTimeout(function() {
                if (btn) {
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Send Reset Instructions';
                }
                if (loading) {
                    loading.style.display = 'none';
                }
            }, 10000);
        });
    }
    
    // Auto-focus on email field
    const emailField = document.getElementById('email');
    if (emailField) {
        emailField.focus();
    }
});
</script>
{% endblock %}
