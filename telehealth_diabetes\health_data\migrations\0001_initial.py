# Generated by Django 5.2.3 on 2025-06-30 15:20

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('patients', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ActivityRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('activity_type', models.CharField(choices=[('walking', 'Walking'), ('running', 'Running'), ('cycling', 'Cycling'), ('swimming', 'Swimming'), ('gym', 'Gym Workout'), ('sports', 'Sports'), ('other', 'Other')], max_length=20)),
                ('duration_minutes', models.IntegerField(help_text='Duration in minutes', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(600)])),
                ('intensity', models.CharField(choices=[('low', 'Low'), ('moderate', 'Moderate'), ('high', 'High')], max_length=10)),
                ('description', models.TextField(blank=True)),
                ('recorded_at', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activity_records', to='patients.patientprofile')),
            ],
            options={
                'ordering': ['-recorded_at'],
            },
        ),
        migrations.CreateModel(
            name='BloodGlucoseReading',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('value', models.FloatField(help_text='Blood glucose in mmol/L', validators=[django.core.validators.MinValueValidator(1.0), django.core.validators.MaxValueValidator(50.0)])),
                ('measurement_type', models.CharField(choices=[('fasting', 'Fasting'), ('before_meal', 'Before Meal'), ('after_meal', 'After Meal'), ('bedtime', 'Bedtime'), ('random', 'Random')], max_length=20)),
                ('measured_at', models.DateTimeField()),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='glucose_readings', to='patients.patientprofile')),
            ],
            options={
                'ordering': ['-measured_at'],
            },
        ),
        migrations.CreateModel(
            name='BloodPressureReading',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('systolic', models.IntegerField(help_text='Systolic pressure (mmHg)', validators=[django.core.validators.MinValueValidator(50), django.core.validators.MaxValueValidator(300)])),
                ('diastolic', models.IntegerField(help_text='Diastolic pressure (mmHg)', validators=[django.core.validators.MinValueValidator(30), django.core.validators.MaxValueValidator(200)])),
                ('measured_at', models.DateTimeField()),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bp_readings', to='patients.patientprofile')),
            ],
            options={
                'ordering': ['-measured_at'],
            },
        ),
        migrations.CreateModel(
            name='CarbIntakeRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('carbs_grams', models.FloatField(help_text='Carbohydrates in grams', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1000.0)])),
                ('meal_type', models.CharField(choices=[('breakfast', 'Breakfast'), ('lunch', 'Lunch'), ('dinner', 'Dinner'), ('snack', 'Snack')], max_length=20)),
                ('food_description', models.TextField(blank=True)),
                ('recorded_at', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='carb_records', to='patients.patientprofile')),
            ],
            options={
                'ordering': ['-recorded_at'],
            },
        ),
        migrations.CreateModel(
            name='SleepRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sleep_start', models.DateTimeField()),
                ('sleep_end', models.DateTimeField()),
                ('quality', models.CharField(choices=[('poor', 'Poor'), ('fair', 'Fair'), ('good', 'Good'), ('excellent', 'Excellent')], max_length=10)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sleep_records', to='patients.patientprofile')),
            ],
            options={
                'ordering': ['-sleep_start'],
            },
        ),
        migrations.CreateModel(
            name='WeightReading',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('weight', models.FloatField(help_text='Weight in kg', validators=[django.core.validators.MinValueValidator(20.0), django.core.validators.MaxValueValidator(500.0)])),
                ('measured_at', models.DateTimeField()),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='weight_readings', to='patients.patientprofile')),
            ],
            options={
                'ordering': ['-measured_at'],
            },
        ),
    ]
