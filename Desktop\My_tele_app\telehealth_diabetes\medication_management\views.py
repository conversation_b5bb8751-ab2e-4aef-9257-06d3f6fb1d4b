from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages

@login_required
def medication_list(request):
    """List patient medications"""
    try:
        patient_profile = request.user.patientprofile
        medications = patient_profile.medications.filter(is_active=True)
    except:
        medications = []
        messages.info(request, 'Please complete your profile first.')

    return render(request, 'medication_management/list.html', {
        'medications': medications
    })

@login_required
def add_medication(request):
    """Add new medication"""
    if request.method == 'POST':
        messages.info(request, 'Medication management functionality will be implemented.')
        return redirect('medication_management:list')

    return render(request, 'medication_management/add.html')

@login_required
def medication_log(request):
    """Medication log"""
    return render(request, 'medication_management/log.html')

@login_required
def reminders(request):
    """Medication reminders"""
    return render(request, 'medication_management/reminders.html')

@login_required
def refill_requests(request):
    """Refill requests"""
    return render(request, 'medication_management/refills.html')

@login_required
def check_interactions(request):
    """Check drug interactions"""
    return render(request, 'medication_management/interactions.html')
