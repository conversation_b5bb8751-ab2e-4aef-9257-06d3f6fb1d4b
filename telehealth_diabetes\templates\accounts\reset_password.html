{% extends 'base.html' %}
{% load static %}

{% block title %}Reset Password - Telehealth Diabetes Care{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                {% if validlink %}
                    <div class="card-header bg-success text-white text-center">
                        <h4 class="mb-0">
                            <i class="fas fa-lock me-2"></i>
                            Reset Your Password
                        </h4>
                    </div>
                    <div class="card-body">
                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}

                        <div class="text-center mb-4">
                            <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                            <p class="text-muted">
                                Hello {{ user.first_name|default:user.username }}! 
                                Enter your new password below.
                            </p>
                        </div>
                        
                        <form method="post" id="resetPasswordForm">
                            {% csrf_token %}
                            <div class="mb-3">
                                <label for="password1" class="form-label">New Password</label>
                                <input type="password" class="form-control" id="password1" name="password1" 
                                       required autofocus placeholder="Enter your new password">
                                <div class="form-text">
                                    Password must be at least 6 characters long.
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password2" class="form-label">Confirm New Password</label>
                                <input type="password" class="form-control" id="password2" name="password2" 
                                       required placeholder="Confirm your new password">
                                <div id="passwordMatch" class="form-text"></div>
                            </div>
                            
                            <!-- Password Strength Indicator -->
                            <div class="mb-3">
                                <div class="password-strength-label small text-muted mb-1">Password Strength:</div>
                                <div class="password-strength-bar">
                                    <div id="strengthBar" class="strength-fill"></div>
                                </div>
                                <div id="strengthText" class="small text-muted mt-1">Enter a password</div>
                            </div>
                            
                            <!-- Loading indicator -->
                            <div id="loadingIndicator" class="text-center mb-3" style="display: none;">
                                <div class="spinner-border spinner-border-sm text-success" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span class="ms-2">Updating your password...</span>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-success" id="resetBtn">
                                    <i class="fas fa-check me-2"></i>
                                    Update Password
                                </button>
                            </div>
                        </form>
                        
                        <hr>
                        
                        <div class="text-center">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                After updating your password, you'll be redirected to the login page.
                            </small>
                        </div>
                    </div>
                {% else %}
                    <div class="card-header bg-danger text-white text-center">
                        <h4 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Invalid Reset Link
                        </h4>
                    </div>
                    <div class="card-body text-center">
                        <i class="fas fa-times-circle fa-4x text-danger mb-4"></i>
                        <h5 class="text-danger mb-3">Password Reset Link Invalid</h5>
                        <p class="text-muted mb-4">
                            This password reset link is invalid or has expired. 
                            Password reset links are only valid for 24 hours for security reasons.
                        </p>
                        
                        <div class="d-grid gap-2">
                            <a href="{% url 'accounts:forgot_password' %}" class="btn btn-warning">
                                <i class="fas fa-redo me-2"></i>
                                Request New Reset Link
                            </a>
                            <a href="{% url 'accounts:login' %}" class="btn btn-outline-primary">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Back to Login
                            </a>
                        </div>
                        
                        <hr>
                        
                        <div class="small text-muted">
                            <p class="mb-2">
                                <strong>Common reasons for invalid links:</strong>
                            </p>
                            <ul class="text-start">
                                <li>Link has expired (older than 24 hours)</li>
                                <li>Link has already been used</li>
                                <li>Link was copied incorrectly</li>
                                <li>Multiple reset requests were made</li>
                            </ul>
                        </div>
                    </div>
                {% endif %}
            </div>
            
            <!-- Security Notice -->
            <div class="card mt-4 border-info">
                <div class="card-body">
                    <h6 class="card-title text-info">
                        <i class="fas fa-shield-alt me-2"></i>
                        Security Notice
                    </h6>
                    <div class="small text-muted">
                        <ul class="mb-0">
                            <li>Choose a strong, unique password</li>
                            <li>Don't reuse passwords from other accounts</li>
                            <li>Consider using a password manager</li>
                            <li>Log out from shared computers</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-control {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
}

.form-control:focus {
    border-color: #198754;
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.card {
    border: none;
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}

.password-strength-bar {
    width: 100%;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: 4px;
}

.strength-weak { background-color: #dc3545; }
.strength-fair { background-color: #fd7e14; }
.strength-good { background-color: #ffc107; }
.strength-strong { background-color: #198754; }

.password-match { color: #198754; }
.password-no-match { color: #dc3545; }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('resetPasswordForm');
    const btn = document.getElementById('resetBtn');
    const loading = document.getElementById('loadingIndicator');
    const password1 = document.getElementById('password1');
    const password2 = document.getElementById('password2');
    const strengthBar = document.getElementById('strengthBar');
    const strengthText = document.getElementById('strengthText');
    const passwordMatch = document.getElementById('passwordMatch');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            const pass1 = password1.value;
            const pass2 = password2.value;
            
            // Validation
            if (!pass1) {
                alert('Please enter a password.');
                e.preventDefault();
                return;
            }
            
            if (pass1.length < 6) {
                alert('Password must be at least 6 characters long.');
                e.preventDefault();
                return;
            }
            
            if (pass1 !== pass2) {
                alert('Passwords do not match.');
                e.preventDefault();
                return;
            }
            
            // Show loading state
            if (btn) {
                btn.disabled = true;
                btn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Updating...';
            }
            
            if (loading) {
                loading.style.display = 'block';
            }
        });
    }
    
    // Password strength checker
    if (password1 && strengthBar && strengthText) {
        password1.addEventListener('input', function() {
            const password = this.value;
            let strength = 0;
            let strengthLabel = '';
            
            if (password.length >= 6) strength += 25;
            if (password.match(/[a-z]/)) strength += 25;
            if (password.match(/[A-Z]/)) strength += 25;
            if (password.match(/[0-9]/)) strength += 25;
            
            strengthBar.style.width = strength + '%';
            
            if (strength === 0) {
                strengthBar.className = 'strength-fill';
                strengthLabel = 'Enter a password';
            } else if (strength <= 25) {
                strengthBar.className = 'strength-fill strength-weak';
                strengthLabel = 'Weak';
            } else if (strength <= 50) {
                strengthBar.className = 'strength-fill strength-fair';
                strengthLabel = 'Fair';
            } else if (strength <= 75) {
                strengthBar.className = 'strength-fill strength-good';
                strengthLabel = 'Good';
            } else {
                strengthBar.className = 'strength-fill strength-strong';
                strengthLabel = 'Strong';
            }
            
            strengthText.textContent = strengthLabel;
        });
    }
    
    // Password confirmation checker
    if (password1 && password2 && passwordMatch) {
        function checkPasswordMatch() {
            const pass1 = password1.value;
            const pass2 = password2.value;
            
            if (pass2.length === 0) {
                passwordMatch.textContent = '';
                passwordMatch.className = 'form-text';
            } else if (pass1 === pass2) {
                passwordMatch.textContent = '✓ Passwords match';
                passwordMatch.className = 'form-text password-match';
            } else {
                passwordMatch.textContent = '✗ Passwords do not match';
                passwordMatch.className = 'form-text password-no-match';
            }
        }
        
        password1.addEventListener('input', checkPasswordMatch);
        password2.addEventListener('input', checkPasswordMatch);
    }
});
</script>
{% endblock %}
