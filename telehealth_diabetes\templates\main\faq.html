{% extends 'base.html' %}
{% load static %}

{% block title %}FAQ - Telehealth Diabetes Care{% endblock %}

{% block content %}
<div class="container py-5">
    <!-- Hero Section -->
    <div class="row mb-5">
        <div class="col-12 text-center">
            <h1 class="display-4 text-primary mb-4">
                <i class="fas fa-question-circle me-3"></i>
                Frequently Asked Questions
            </h1>
            <p class="lead">Find answers to common questions about our telehealth diabetes care platform</p>
        </div>
    </div>

    <!-- Search -->
    <div class="row mb-5">
        <div class="col-lg-6 mx-auto">
            <div class="input-group">
                <input type="text" class="form-control" placeholder="Search FAQ...">
                <button class="btn btn-primary" type="button">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- FAQ Categories -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="d-flex flex-wrap justify-content-center gap-2 mb-4">
                <button class="btn btn-outline-primary active" data-category="all">All</button>
                <button class="btn btn-outline-primary" data-category="getting-started">Getting Started</button>
                <button class="btn btn-outline-primary" data-category="account">Account</button>
                <button class="btn btn-outline-primary" data-category="health-data">Health Data</button>
                <button class="btn btn-outline-primary" data-category="appointments">Appointments</button>
                <button class="btn btn-outline-primary" data-category="billing">Billing</button>
                <button class="btn btn-outline-primary" data-category="technical">Technical</button>
            </div>
        </div>
    </div>

    <!-- FAQ Accordion -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="accordion" id="faqAccordion">
                
                <!-- Getting Started -->
                <div class="accordion-item" data-category="getting-started">
                    <h2 class="accordion-header">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                            How do I get started with DiabetesCare?
                        </button>
                    </h2>
                    <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            Getting started is easy! Simply click the "Register" button, create your account, and complete your patient profile. Once registered, you can immediately start tracking your health data, schedule appointments, and access our educational resources.
                        </div>
                    </div>
                </div>

                <div class="accordion-item" data-category="getting-started">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                            Is DiabetesCare suitable for all types of diabetes?
                        </button>
                    </h2>
                    <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            Yes! Our platform supports patients with Type 1, Type 2, and gestational diabetes. We also provide resources for pre-diabetes management. Our educational content and tools are tailored to address the specific needs of each diabetes type.
                        </div>
                    </div>
                </div>

                <!-- Account -->
                <div class="accordion-item" data-category="account">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                            How do I update my profile information?
                        </button>
                    </h2>
                    <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            You can update your profile by clicking on your username in the top navigation, then selecting "My Profile." From there, you can edit your personal information, emergency contacts, and diabetes management preferences.
                        </div>
                    </div>
                </div>

                <div class="accordion-item" data-category="account">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                            Can I delete my account?
                        </button>
                    </h2>
                    <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            Yes, you can request account deletion by contacting our support team. Please note that this action is permanent and will remove all your health data from our system. We recommend downloading your data before deletion.
                        </div>
                    </div>
                </div>

                <!-- Health Data -->
                <div class="accordion-item" data-category="health-data">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq5">
                            How do I track my blood glucose readings?
                        </button>
                    </h2>
                    <div id="faq5" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            Navigate to "Health Data" from your dashboard and click "Add Reading." Enter your glucose value, select the reading type (fasting, post-meal, etc.), and add any notes. You can also view trends and charts of your readings over time.
                        </div>
                    </div>
                </div>

                <div class="accordion-item" data-category="health-data">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq6">
                            Can I import data from my glucose meter or CGM?
                        </button>
                    </h2>
                    <div id="faq6" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            We're working on integrations with popular glucose meters and CGM devices. Currently, you can manually enter your readings or upload CSV files from compatible devices. Check our integrations page for the latest supported devices.
                        </div>
                    </div>
                </div>

                <!-- Appointments -->
                <div class="accordion-item" data-category="appointments">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq7">
                            How do I schedule a telehealth appointment?
                        </button>
                    </h2>
                    <div id="faq7" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            Go to "Appointments" in your dashboard and click "Schedule New Appointment." Choose your preferred provider, select an available time slot, and describe your reason for the visit. You'll receive a confirmation email with the video call link.
                        </div>
                    </div>
                </div>

                <div class="accordion-item" data-category="appointments">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq8">
                            What do I need for a video appointment?
                        </button>
                    </h2>
                    <div id="faq8" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            You'll need a device with a camera and microphone (computer, tablet, or smartphone), a stable internet connection, and a quiet, private space. We recommend testing your audio and video before the appointment.
                        </div>
                    </div>
                </div>

                <!-- Billing -->
                <div class="accordion-item" data-category="billing">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq9">
                            How much does DiabetesCare cost?
                        </button>
                    </h2>
                    <div id="faq9" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            Basic health tracking and educational resources are free. Telehealth consultations are billed per session or through monthly subscription plans. We accept most insurance plans and offer affordable self-pay options.
                        </div>
                    </div>
                </div>

                <div class="accordion-item" data-category="billing">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq10">
                            Do you accept insurance?
                        </button>
                    </h2>
                    <div id="faq10" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            Yes, we accept most major insurance plans including Medicare and Medicaid. During registration, you can verify your insurance coverage. We'll handle the billing process and provide you with necessary documentation for reimbursement.
                        </div>
                    </div>
                </div>

                <!-- Technical -->
                <div class="accordion-item" data-category="technical">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq11">
                            What browsers are supported?
                        </button>
                    </h2>
                    <div id="faq11" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            DiabetesCare works best on modern browsers including Chrome, Firefox, Safari, and Edge. We recommend keeping your browser updated for the best experience and security.
                        </div>
                    </div>
                </div>

                <div class="accordion-item" data-category="technical">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq12">
                            Is my health data secure?
                        </button>
                    </h2>
                    <div id="faq12" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            Absolutely. We use bank-level encryption and are fully HIPAA compliant. Your data is stored securely and never shared without your explicit consent. We undergo regular security audits to ensure your information remains protected.
                        </div>
                    </div>
                </div>

                <div class="accordion-item" data-category="technical">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq13">
                            I'm having trouble logging in. What should I do?
                        </button>
                    </h2>
                    <div id="faq13" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            First, try resetting your password using the "Forgot Password" link on the login page. If you're still having trouble, clear your browser cache and cookies, or try a different browser. Contact our support team if the issue persists.
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- Contact Support -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-0 shadow-lg bg-primary text-white">
                <div class="card-body p-5 text-center">
                    <h2 class="mb-4">Still Have Questions?</h2>
                    <p class="lead mb-4">Our support team is here to help you 24/7</p>
                    <div class="row justify-content-center">
                        <div class="col-md-4 mb-3">
                            <div class="card bg-white text-dark">
                                <div class="card-body">
                                    <i class="fas fa-phone fa-2x text-primary mb-3"></i>
                                    <h5>Call Us</h5>
                                    <p>+254 (0) 123-456-789</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-white text-dark">
                                <div class="card-body">
                                    <i class="fas fa-envelope fa-2x text-primary mb-3"></i>
                                    <h5>Email Us</h5>
                                    <p><EMAIL></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-white text-dark">
                                <div class="card-body">
                                    <i class="fas fa-comments fa-2x text-primary mb-3"></i>
                                    <h5>Live Chat</h5>
                                    <p>Available 24/7</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <a href="{% url 'main:contact' %}" class="btn btn-light btn-lg mt-3">
                        <i class="fas fa-envelope me-2"></i>
                        Contact Support
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Category filtering
    const categoryButtons = document.querySelectorAll('[data-category]');
    const faqItems = document.querySelectorAll('.accordion-item[data-category]');
    
    categoryButtons.forEach(button => {
        button.addEventListener('click', function() {
            const category = this.dataset.category;
            
            // Update active button
            categoryButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter FAQ items
            faqItems.forEach(item => {
                if (category === 'all' || item.dataset.category === category) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });
});
</script>
{% endblock %}
