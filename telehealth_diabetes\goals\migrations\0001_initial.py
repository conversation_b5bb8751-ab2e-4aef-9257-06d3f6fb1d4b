# Generated by Django 5.2.3 on 2025-06-30 15:20

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('patients', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Achievement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('achievement_type', models.CharField(choices=[('goal_completion', 'Goal Completion'), ('streak', 'Streak Achievement'), ('milestone', 'Milestone Achievement'), ('participation', 'Participation'), ('improvement', 'Improvement')], max_length=20)),
                ('icon', models.Char<PERSON>ield(blank=True, max_length=50)),
                ('badge_color', models.Char<PERSON>ield(default='#ffd700', max_length=7)),
                ('points', models.IntegerField(default=10)),
                ('criteria_description', models.TextField(help_text='What needs to be done to earn this')),
                ('is_repeatable', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='GoalCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('icon', models.CharField(blank=True, max_length=50)),
                ('color', models.CharField(default='#007bff', help_text='Hex color code', max_length=7)),
            ],
            options={
                'verbose_name_plural': 'Goal Categories',
            },
        ),
        migrations.CreateModel(
            name='GoalTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('goal_type', models.CharField(choices=[('blood_glucose', 'Blood Glucose Management'), ('weight', 'Weight Management'), ('exercise', 'Exercise & Activity'), ('medication', 'Medication Adherence'), ('diet', 'Diet & Nutrition'), ('sleep', 'Sleep Quality'), ('stress', 'Stress Management'), ('education', 'Diabetes Education'), ('custom', 'Custom Goal')], max_length=20)),
                ('default_target_value', models.FloatField(blank=True, null=True)),
                ('default_target_unit', models.CharField(blank=True, max_length=20)),
                ('default_duration_days', models.IntegerField(default=30)),
                ('tips', models.TextField(blank=True, help_text='Tips for achieving this goal')),
                ('is_active', models.BooleanField(default=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='HealthGoal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('goal_type', models.CharField(choices=[('blood_glucose', 'Blood Glucose Management'), ('weight', 'Weight Management'), ('exercise', 'Exercise & Activity'), ('medication', 'Medication Adherence'), ('diet', 'Diet & Nutrition'), ('sleep', 'Sleep Quality'), ('stress', 'Stress Management'), ('education', 'Diabetes Education'), ('custom', 'Custom Goal')], max_length=20)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('target_value', models.FloatField(blank=True, help_text='Numeric target if applicable', null=True)),
                ('target_unit', models.CharField(blank=True, help_text='e.g., kg, mmol/L, minutes', max_length=20)),
                ('current_value', models.FloatField(blank=True, null=True)),
                ('start_date', models.DateField()),
                ('target_date', models.DateField()),
                ('status', models.CharField(choices=[('active', 'Active'), ('completed', 'Completed'), ('paused', 'Paused'), ('cancelled', 'Cancelled')], default='active', max_length=20)),
                ('progress_percentage', models.IntegerField(default=0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('motivation', models.TextField(blank=True, help_text='Why this goal is important')),
                ('reward', models.CharField(blank=True, help_text='Reward for achieving goal', max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='goals.goalcategory')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='goals', to='patients.patientprofile')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GoalMilestone',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('target_value', models.FloatField(blank=True, null=True)),
                ('target_date', models.DateField()),
                ('is_completed', models.BooleanField(default=False)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('goal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='milestones', to='goals.healthgoal')),
            ],
            options={
                'ordering': ['target_date'],
            },
        ),
        migrations.CreateModel(
            name='GoalProgress',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('value', models.FloatField(blank=True, null=True)),
                ('notes', models.TextField(blank=True)),
                ('mood', models.CharField(blank=True, choices=[('excellent', 'Excellent'), ('good', 'Good'), ('okay', 'Okay'), ('difficult', 'Difficult'), ('struggling', 'Struggling')], max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('goal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='progress_entries', to='goals.healthgoal')),
            ],
            options={
                'ordering': ['-date'],
                'unique_together': {('goal', 'date')},
            },
        ),
        migrations.CreateModel(
            name='PatientAchievement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('earned_at', models.DateTimeField(auto_now_add=True)),
                ('notes', models.TextField(blank=True)),
                ('achievement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='goals.achievement')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='achievements', to='patients.patientprofile')),
                ('related_goal', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='goals.healthgoal')),
            ],
            options={
                'ordering': ['-earned_at'],
                'unique_together': {('patient', 'achievement', 'related_goal')},
            },
        ),
    ]
