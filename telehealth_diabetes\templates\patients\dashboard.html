{% extends 'base.html' %}
{% load static %}

{% block title %}Patient Dashboard - Telehealth Diabetes Care{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-tachometer-alt me-2 text-primary"></i>
                    Welcome back, {{ user.get_full_name|default:user.username }}!
                </h1>
                <div class="text-muted">
                    <i class="fas fa-calendar me-1"></i>
                    {{ "now"|date:"F d, Y" }}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Health Summary -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-tint fa-2x"></i>
                    </div>
                    <h6 class="card-title text-muted">Latest Glucose</h6>
                    {% if recent_glucose %}
                        <h4 class="text-primary mb-0">{{ recent_glucose.value }} mmol/L</h4>
                        <small class="text-muted">{{ recent_glucose.measured_at|timesince }} ago</small>
                    {% else %}
                        <h4 class="text-muted mb-0">No data</h4>
                        <small class="text-muted">Record your first reading</small>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-weight fa-2x"></i>
                    </div>
                    <h6 class="card-title text-muted">Current Weight</h6>
                    {% if recent_weight %}
                        <h4 class="text-success mb-0">{{ recent_weight.weight }} kg</h4>
                        <small class="text-muted">{{ recent_weight.measured_at|timesince }} ago</small>
                    {% else %}
                        <h4 class="text-muted mb-0">No data</h4>
                        <small class="text-muted">Record your weight</small>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-info mb-2">
                        <i class="fas fa-calendar-check fa-2x"></i>
                    </div>
                    <h6 class="card-title text-muted">Next Appointment</h6>
                    {% if upcoming_appointments %}
                        <h4 class="text-info mb-0">{{ upcoming_appointments.0.scheduled_datetime|date:"M d" }}</h4>
                        <small class="text-muted">{{ upcoming_appointments.0.scheduled_datetime|timeuntil }}</small>
                    {% else %}
                        <h4 class="text-muted mb-0">None</h4>
                        <small class="text-muted">Schedule one now</small>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-bullseye fa-2x"></i>
                    </div>
                    <h6 class="card-title text-muted">Active Goals</h6>
                    <h4 class="text-warning mb-0">{{ active_goals|length }}</h4>
                    <small class="text-muted">goals in progress</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Dashboard Content -->
    <div class="row">
        <!-- Left Column -->
        <div class="col-lg-8">
            <!-- Upcoming Appointments -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2 text-primary"></i>
                        Upcoming Appointments
                    </h5>
                    <a href="{% url 'appointments:schedule' %}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-plus me-1"></i>Schedule New
                    </a>
                </div>
                <div class="card-body">
                    {% if upcoming_appointments %}
                        {% for appointment in upcoming_appointments %}
                            <div class="d-flex align-items-center p-3 border rounded mb-2">
                                <div class="me-3">
                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                        <i class="fas fa-user-md"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ appointment.provider.user.get_full_name }}</h6>
                                    <p class="mb-1 text-muted">{{ appointment.get_appointment_type_display }}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ appointment.scheduled_datetime|date:"M d, Y \a\t g:i A" }}
                                    </small>
                                </div>
                                <div>
                                    <a href="{% url 'appointments:detail' appointment.id %}" class="btn btn-sm btn-outline-primary">View</a>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No upcoming appointments</p>
                            <a href="{% url 'appointments:schedule' %}" class="btn btn-primary">Schedule Your First Appointment</a>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Active Goals -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-bullseye me-2 text-warning"></i>
                        My Goals
                    </h5>
                    <a href="{% url 'goals:create' %}" class="btn btn-sm btn-outline-warning">
                        <i class="fas fa-plus me-1"></i>New Goal
                    </a>
                </div>
                <div class="card-body">
                    {% if active_goals %}
                        {% for goal in active_goals %}
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0">{{ goal.title }}</h6>
                                    <span class="badge bg-warning">{{ goal.progress_percentage }}%</span>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-warning" style="width: {{ goal.progress_percentage }}%"></div>
                                </div>
                                <small class="text-muted">Target: {{ goal.target_date|date:"M d, Y" }}</small>
                            </div>
                        {% endfor %}
                        <div class="text-center mt-3">
                            <a href="{% url 'goals:list' %}" class="btn btn-sm btn-outline-warning">View All Goals</a>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-bullseye fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No active goals</p>
                            <a href="{% url 'goals:create' %}" class="btn btn-warning">Set Your First Goal</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2 text-success"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'health_data:glucose' %}" class="btn btn-outline-primary">
                            <i class="fas fa-tint me-2"></i>Record Blood Glucose
                        </a>
                        <a href="{% url 'medication_management:log' %}" class="btn btn-outline-success">
                            <i class="fas fa-pills me-2"></i>Log Medication
                        </a>
                        <a href="{% url 'health_data:weight' %}" class="btn btn-outline-info">
                            <i class="fas fa-weight me-2"></i>Record Weight
                        </a>
                        <a href="{% url 'mental_health:mood_tracking' %}" class="btn btn-outline-warning">
                            <i class="fas fa-smile me-2"></i>Track Mood
                        </a>
                    </div>
                </div>
            </div>

            <!-- Medication Reminders -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-pills me-2 text-danger"></i>
                        Medication Reminders
                    </h5>
                </div>
                <div class="card-body">
                    {% if active_medications %}
                        {% for medication in active_medications %}
                            <div class="d-flex align-items-center mb-3">
                                <div class="me-3">
                                    <i class="fas fa-pill text-danger"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-0">{{ medication.medication.name }}</h6>
                                    <small class="text-muted">{{ medication.dosage }} - {{ medication.frequency }}</small>
                                </div>
                            </div>
                        {% endfor %}
                        <div class="text-center">
                            <a href="{% url 'medication_management:list' %}" class="btn btn-sm btn-outline-danger">View All</a>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-pills fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No medications added</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Educational Resources -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-graduation-cap me-2 text-info"></i>
                        Recommended Learning
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="mb-1">Understanding Blood Sugar</h6>
                        <small class="text-muted">Learn how to interpret your readings</small>
                    </div>
                    <div class="mb-3">
                        <h6 class="mb-1">Healthy Meal Planning</h6>
                        <small class="text-muted">Recipes and nutrition tips</small>
                    </div>
                    <div class="text-center">
                        <a href="{% url 'education:home' %}" class="btn btn-sm btn-outline-info">Explore All Resources</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
