{% extends 'base.html' %}
{% load static %}

{% block title %}{{ category.name }} - Education Library{% endblock %}

{% block extra_css %}
<style>
.category-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 3rem 2rem;
    margin-bottom: 2rem;
}

.education-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    height: 100%;
}

.education-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.content-type-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
}

.difficulty-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
}

.content-image {
    height: 200px;
    object-fit: cover;
    border-radius: 15px 15px 0 0;
}

.category-stats {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'education:home' %}">Education</a></li>
            <li class="breadcrumb-item"><a href="{% url 'education:library' %}">Library</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ category.name }}</li>
        </ol>
    </nav>

    <!-- Category Header -->
    <div class="category-header">
        <div class="text-center">
            <h1 class="display-5 fw-bold mb-2">
                {% if category.icon %}
                    <i class="{{ category.icon }} me-3"></i>
                {% else %}
                    <i class="fas fa-folder me-3"></i>
                {% endif %}
                {{ category.name }}
            </h1>
            {% if category.description %}
                <p class="lead">{{ category.description }}</p>
            {% endif %}
        </div>
    </div>

    <!-- Category Stats -->
    <div class="category-stats">
        <div class="row text-center">
            <div class="col-md-4">
                <h3 class="fw-bold">{{ content.paginator.count }}</h3>
                <p class="mb-0">Content Item{{ content.paginator.count|pluralize }}</p>
            </div>
            <div class="col-md-4">
                <h3 class="fw-bold">{{ category.name }}</h3>
                <p class="mb-0">Category Focus</p>
            </div>
            <div class="col-md-4">
                <h3 class="fw-bold">Free</h3>
                <p class="mb-0">Access Level</p>
            </div>
        </div>
    </div>

    <!-- Content Grid -->
    <div class="row">
        {% for item in content %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card education-card">
                    <div class="position-relative">
                        {% if item.image %}
                            <img src="{{ item.image.url }}" class="card-img-top content-image" alt="{{ item.title }}">
                        {% else %}
                            <div class="content-image bg-gradient-primary d-flex align-items-center justify-content-center">
                                <i class="fas fa-book fa-3x text-white"></i>
                            </div>
                        {% endif %}
                        
                        <!-- Content Type Badge -->
                        <span class="badge content-type-badge 
                            {% if item.content_type == 'article' %}bg-primary
                            {% elif item.content_type == 'video' %}bg-danger
                            {% elif item.content_type == 'quiz' %}bg-success
                            {% elif item.content_type == 'recipe' %}bg-warning
                            {% else %}bg-info{% endif %}">
                            {% if item.content_type == 'article' %}<i class="fas fa-file-alt me-1"></i>
                            {% elif item.content_type == 'video' %}<i class="fas fa-play me-1"></i>
                            {% elif item.content_type == 'quiz' %}<i class="fas fa-question-circle me-1"></i>
                            {% elif item.content_type == 'recipe' %}<i class="fas fa-utensils me-1"></i>
                            {% else %}<i class="fas fa-info-circle me-1"></i>{% endif %}
                            {{ item.get_content_type_display }}
                        </span>
                    </div>
                    
                    <div class="card-body d-flex flex-column">
                        <div class="mb-2">
                            <span class="difficulty-badge 
                                {% if item.difficulty_level == 'beginner' %}bg-success text-white
                                {% elif item.difficulty_level == 'intermediate' %}bg-warning text-dark
                                {% else %}bg-danger text-white{% endif %}">
                                {{ item.get_difficulty_level_display }}
                            </span>
                        </div>
                        
                        <h5 class="card-title">{{ item.title }}</h5>
                        <p class="card-text text-muted flex-grow-1">{{ item.summary|truncatewords:20 }}</p>
                        
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            {% if item.estimated_read_time %}
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>{{ item.estimated_read_time }} min read
                                </small>
                            {% endif %}
                            <small class="text-muted">
                                <i class="fas fa-eye me-1"></i>{{ item.view_count }} views
                            </small>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <a href="{% url 'education:content_detail' item.slug %}" 
                               class="btn btn-primary flex-grow-1">
                                <i class="fas fa-book-open me-2"></i>Read More
                            </a>
                            {% if user.is_authenticated %}
                                <button class="btn btn-outline-secondary bookmark-btn" 
                                        data-content-id="{{ item.id }}"
                                        title="Bookmark">
                                    <i class="fas fa-bookmark"></i>
                                </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        {% empty %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-folder-open fa-4x text-muted mb-4"></i>
                    <h4 class="text-muted">No Content Available</h4>
                    <p class="text-muted">
                        There is no content available in the "{{ category.name }}" category at the moment.
                        Check back soon for new educational materials!
                    </p>
                    <a href="{% url 'education:library' %}" class="btn btn-primary">
                        <i class="fas fa-arrow-left me-2"></i>Browse All Content
                    </a>
                </div>
            </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if content.has_other_pages %}
        <div class="row mt-4">
            <div class="col-12">
                <nav aria-label="Category content pagination">
                    <ul class="pagination justify-content-center">
                        {% if content.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ content.previous_page_number }}">
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ content.number }} of {{ content.paginator.num_pages }}
                            </span>
                        </li>
                        
                        {% if content.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ content.next_page_number }}">
                                    <i class="fas fa-angle-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ content.paginator.num_pages }}">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </div>
    {% endif %}

    <!-- Related Categories -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <h5 class="card-title">
                        <i class="fas fa-compass me-2"></i>
                        Explore Other Categories
                    </h5>
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <a href="{% url 'education:library' %}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-book me-2"></i>All Content
                            </a>
                        </div>
                        <div class="col-md-4 mb-2">
                            <a href="{% url 'education:recipes' %}" class="btn btn-outline-warning w-100">
                                <i class="fas fa-utensils me-2"></i>Recipes
                            </a>
                        </div>
                        <div class="col-md-4 mb-2">
                            <a href="{% url 'education:exercises' %}" class="btn btn-outline-success w-100">
                                <i class="fas fa-dumbbell me-2"></i>Exercises
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Bookmark functionality
    const bookmarkBtns = document.querySelectorAll('.bookmark-btn');
    bookmarkBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const contentId = this.dataset.contentId;
            
            fetch(`{% url 'education:bookmark' 0 %}`.replace('0', contentId), {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.classList.add('btn-warning');
                    this.classList.remove('btn-outline-secondary');
                    
                    // Show success message
                    const alert = document.createElement('div');
                    alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
                    alert.style.top = '20px';
                    alert.style.right = '20px';
                    alert.style.zIndex = '9999';
                    alert.innerHTML = `
                        <i class="fas fa-check me-2"></i>Content bookmarked!
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.body.appendChild(alert);
                    
                    setTimeout(() => {
                        alert.remove();
                    }, 3000);
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        });
    });
});
</script>
{% endblock %}
