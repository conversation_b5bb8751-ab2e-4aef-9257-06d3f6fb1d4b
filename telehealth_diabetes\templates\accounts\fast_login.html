{% extends 'base.html' %}
{% load static %}

{% block title %}Fast Login - Telehealth Diabetes Care{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        Quick Login
                    </h4>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    
                    <form method="post" id="fastLoginForm">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" name="username" 
                                   value="{{ form.username }}" required autofocus
                                   placeholder="Enter your username">
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" 
                                   required placeholder="Enter your password">
                        </div>
                        
                        <!-- Loading indicator -->
                        <div id="fastLoadingIndicator" class="text-center mb-3" style="display: none;">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <span class="ms-2">Signing you in...</span>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary" id="fastLoginBtn">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Login
                            </button>
                        </div>
                        
                        <!-- Quick test credentials for development -->
                        {% if debug %}
                        <div class="mt-3 p-2 bg-light rounded">
                            <small class="text-muted d-block mb-2">Test Credentials:</small>
                            <div class="d-grid gap-1">
                                <button type="button" class="btn btn-outline-secondary btn-sm" 
                                        onclick="quickFill('demo', 'demo123')">
                                    Demo User (demo/demo123)
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" 
                                        onclick="quickFill('testpatient', 'testpass123')">
                                    Test Patient
                                </button>
                            </div>
                        </div>
                        {% endif %}
                    </form>
                    
                    <hr>

                    <div class="text-center">
                        <div class="mb-3">
                            <a href="{% url 'accounts:forgot_password' %}" class="text-decoration-none">
                                <i class="fas fa-key me-1"></i>
                                Forgot your password?
                            </a>
                        </div>
                        <p class="mb-0">Don't have an account?</p>
                        <a href="{% url 'accounts:fast_register' %}" class="btn btn-outline-primary">
                            <i class="fas fa-user-plus me-2"></i>
                            Register
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-control {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('fastLoginForm');
    const btn = document.getElementById('fastLoginBtn');
    const loading = document.getElementById('fastLoadingIndicator');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            // Show loading immediately
            if (btn) {
                btn.disabled = true;
                btn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Logging in...';
            }
            
            if (loading) {
                loading.style.display = 'block';
            }
            
            // Re-enable after timeout (safety)
            setTimeout(function() {
                if (btn) {
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>Login';
                }
                if (loading) {
                    loading.style.display = 'none';
                }
            }, 5000);
        });
    }
    
    // Enter key support
    const passwordField = document.getElementById('password');
    if (passwordField) {
        passwordField.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                form.submit();
            }
        });
    }
});

// Quick fill function for testing
function quickFill(username, password) {
    document.getElementById('username').value = username;
    document.getElementById('password').value = password;
    document.getElementById('fastLoginForm').submit();
}
</script>
{% endblock %}
