# Generated by Django 5.2.3 on 2025-06-30 15:20

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('patients', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AssessmentQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question_text', models.TextField()),
                ('question_type', models.CharField(choices=[('multiple_choice', 'Multiple Choice'), ('scale', 'Rating Scale'), ('yes_no', 'Yes/No'), ('text', 'Text Response')], max_length=20)),
                ('order', models.IntegerField()),
                ('is_required', models.BooleanField(default=True)),
                ('scale_min', models.IntegerField(blank=True, null=True)),
                ('scale_max', models.IntegerField(blank=True, null=True)),
                ('scale_min_label', models.Char<PERSON>ield(blank=True, max_length=100)),
                ('scale_max_label', models.CharField(blank=True, max_length=100)),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='AssessmentChoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('choice_text', models.CharField(max_length=200)),
                ('score_value', models.IntegerField(default=0)),
                ('order', models.IntegerField()),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='choices', to='mental_health.assessmentquestion')),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='CopingStrategy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('strategy_type', models.CharField(choices=[('breathing', 'Breathing Exercise'), ('meditation', 'Meditation'), ('physical', 'Physical Activity'), ('cognitive', 'Cognitive Technique'), ('social', 'Social Support'), ('creative', 'Creative Activity'), ('relaxation', 'Relaxation Technique')], max_length=20)),
                ('instructions', models.TextField()),
                ('duration_minutes', models.IntegerField(blank=True, null=True)),
                ('difficulty_level', models.CharField(choices=[('easy', 'Easy'), ('moderate', 'Moderate'), ('challenging', 'Challenging')], default='easy', max_length=20)),
                ('best_for', models.CharField(blank=True, help_text="e.g., 'acute stress', 'before medical appointments'", max_length=200)),
                ('is_published', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='coping_strategies', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='MentalHealthResource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('resource_type', models.CharField(choices=[('article', 'Article'), ('video', 'Video'), ('audio', 'Audio/Meditation'), ('exercise', 'Exercise/Technique'), ('assessment', 'Self-Assessment'), ('external_link', 'External Resource')], max_length=20)),
                ('topic', models.CharField(choices=[('stress', 'Stress Management'), ('anxiety', 'Anxiety'), ('depression', 'Depression'), ('coping', 'Coping Strategies'), ('mindfulness', 'Mindfulness'), ('sleep', 'Sleep Issues'), ('relationships', 'Relationships'), ('diabetes_distress', 'Diabetes Distress')], max_length=30)),
                ('content', models.TextField(blank=True)),
                ('external_url', models.URLField(blank=True)),
                ('duration_minutes', models.IntegerField(blank=True, null=True)),
                ('difficulty_level', models.CharField(choices=[('beginner', 'Beginner'), ('intermediate', 'Intermediate'), ('advanced', 'Advanced')], default='beginner', max_length=20)),
                ('is_published', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='mental_health_resources', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='PatientAssessmentResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_score', models.IntegerField()),
                ('max_possible_score', models.IntegerField()),
                ('interpretation', models.TextField(blank=True)),
                ('recommendations', models.TextField(blank=True)),
                ('taken_at', models.DateTimeField(auto_now_add=True)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assessment_results', to='patients.patientprofile')),
            ],
            options={
                'ordering': ['-taken_at'],
            },
        ),
        migrations.CreateModel(
            name='AssessmentResponse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('scale_value', models.IntegerField(blank=True, null=True)),
                ('text_response', models.TextField(blank=True)),
                ('choice', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='mental_health.assessmentchoice')),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mental_health.assessmentquestion')),
                ('result', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='responses', to='mental_health.patientassessmentresult')),
            ],
        ),
        migrations.CreateModel(
            name='PatientCopingLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('used_at', models.DateTimeField()),
                ('effectiveness_rating', models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], help_text='1 = Not helpful, 5 = Very helpful')),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='coping_logs', to='patients.patientprofile')),
                ('strategy', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mental_health.copingstrategy')),
            ],
            options={
                'ordering': ['-used_at'],
            },
        ),
        migrations.CreateModel(
            name='SelfAssessment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('assessment_type', models.CharField(choices=[('phq9', 'PHQ-9 (Depression)'), ('gad7', 'GAD-7 (Anxiety)'), ('stress', 'Stress Level'), ('diabetes_distress', 'Diabetes Distress'), ('sleep_quality', 'Sleep Quality'), ('custom', 'Custom Assessment')], max_length=30)),
                ('instructions', models.TextField()),
                ('disclaimer', models.TextField(default='This is not a diagnostic tool. Please consult a healthcare professional for proper evaluation.')),
                ('is_active', models.BooleanField(default=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='patientassessmentresult',
            name='assessment',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mental_health.selfassessment'),
        ),
        migrations.AddField(
            model_name='assessmentquestion',
            name='assessment',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='questions', to='mental_health.selfassessment'),
        ),
        migrations.CreateModel(
            name='MoodEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('mood_rating', models.IntegerField(choices=[(1, 'Very Poor'), (2, 'Poor'), (3, 'Fair'), (4, 'Good'), (5, 'Very Good')])),
                ('energy_level', models.IntegerField(choices=[(1, 'Very Low'), (2, 'Low'), (3, 'Moderate'), (4, 'High'), (5, 'Very High')])),
                ('stress_level', models.IntegerField(help_text='1 = No stress, 10 = Extremely stressed', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(10)])),
                ('sleep_quality', models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], help_text='1 = Very poor, 5 = Excellent')),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='mood_entries', to='patients.patientprofile')),
            ],
            options={
                'ordering': ['-date'],
                'unique_together': {('patient', 'date')},
            },
        ),
    ]
