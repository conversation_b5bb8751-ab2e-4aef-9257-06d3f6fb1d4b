# Generated by Django 5.2.3 on 2025-06-30 15:20

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('patients', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EducationCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('icon', models.CharField(blank=True, help_text='CSS icon class', max_length=50)),
                ('order', models.IntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name_plural': 'Education Categories',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='EducationContent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('slug', models.SlugField(unique=True)),
                ('content_type', models.CharField(choices=[('article', 'Article'), ('video', 'Video'), ('infographic', 'Infographic'), ('quiz', 'Quiz'), ('checklist', 'Checklist'), ('recipe', 'Recipe'), ('exercise', 'Exercise Routine')], max_length=20)),
                ('difficulty_level', models.CharField(choices=[('beginner', 'Beginner'), ('intermediate', 'Intermediate'), ('advanced', 'Advanced')], default='beginner', max_length=20)),
                ('summary', models.TextField(help_text='Brief summary for listings')),
                ('content', models.TextField(help_text='Main content (HTML allowed)')),
                ('video_url', models.URLField(blank=True)),
                ('image', models.ImageField(blank=True, upload_to='education/images/')),
                ('tags', models.CharField(blank=True, help_text='Comma-separated tags', max_length=200)),
                ('estimated_read_time', models.IntegerField(blank=True, help_text='Minutes', null=True)),
                ('diabetes_types', models.CharField(blank=True, help_text='Comma-separated: type1,type2,gestational', max_length=100)),
                ('target_audience', models.CharField(blank=True, help_text='e.g., newly_diagnosed,experienced', max_length=100)),
                ('is_published', models.BooleanField(default=False)),
                ('is_featured', models.BooleanField(default=False)),
                ('view_count', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('published_at', models.DateTimeField(blank=True, null=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='authored_content', to=settings.AUTH_USER_MODEL)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='content', to='education.educationcategory')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ExerciseRoutine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('slug', models.SlugField(unique=True)),
                ('description', models.TextField()),
                ('instructions', models.TextField()),
                ('duration_minutes', models.IntegerField()),
                ('intensity_level', models.CharField(choices=[('low', 'Low Intensity'), ('moderate', 'Moderate Intensity'), ('high', 'High Intensity')], max_length=20)),
                ('equipment_needed', models.TextField(blank=True)),
                ('target_muscle_groups', models.CharField(blank=True, max_length=200)),
                ('suitable_for_beginners', models.BooleanField(default=True)),
                ('requires_supervision', models.BooleanField(default=False)),
                ('contraindications', models.TextField(blank=True, help_text='When not to do this exercise')),
                ('video_url', models.URLField(blank=True)),
                ('image', models.ImageField(blank=True, upload_to='exercises/')),
                ('is_published', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='exercise_routines', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Recipe',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('slug', models.SlugField(unique=True)),
                ('description', models.TextField()),
                ('ingredients', models.TextField(help_text='One ingredient per line')),
                ('instructions', models.TextField(help_text='Step-by-step instructions')),
                ('servings', models.IntegerField()),
                ('prep_time_minutes', models.IntegerField()),
                ('cook_time_minutes', models.IntegerField()),
                ('calories_per_serving', models.IntegerField(blank=True, null=True)),
                ('carbs_per_serving', models.FloatField(blank=True, help_text='Grams', null=True)),
                ('protein_per_serving', models.FloatField(blank=True, help_text='Grams', null=True)),
                ('fat_per_serving', models.FloatField(blank=True, help_text='Grams', null=True)),
                ('fiber_per_serving', models.FloatField(blank=True, help_text='Grams', null=True)),
                ('meal_type', models.CharField(choices=[('breakfast', 'Breakfast'), ('lunch', 'Lunch'), ('dinner', 'Dinner'), ('snack', 'Snack'), ('dessert', 'Dessert')], max_length=20)),
                ('difficulty_level', models.CharField(choices=[('easy', 'Easy'), ('medium', 'Medium'), ('hard', 'Hard')], default='easy', max_length=20)),
                ('image', models.ImageField(blank=True, upload_to='recipes/')),
                ('is_published', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recipes', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='PatientProgress',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('progress_percentage', models.IntegerField(default=0)),
                ('time_spent_minutes', models.IntegerField(default=0)),
                ('rating', models.IntegerField(blank=True, choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], null=True)),
                ('notes', models.TextField(blank=True)),
                ('content', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='patient_progress', to='education.educationcontent')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='education_progress', to='patients.patientprofile')),
            ],
            options={
                'ordering': ['-started_at'],
                'unique_together': {('patient', 'content')},
            },
        ),
    ]
