# Generated by Django 5.2.3 on 2025-06-30 15:20

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('patients', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ProviderMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('is_urgent', models.BooleanField(default=False)),
                ('is_read', models.BooleanField(default=False)),
                ('sent_at', models.DateTimeField(auto_now_add=True)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('parent_message', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='replies', to='providers.providermessage')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_provider_messages', to=settings.AUTH_USER_MODEL)),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_provider_messages', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-sent_at'],
            },
        ),
        migrations.CreateModel(
            name='ProviderProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('specialization', models.CharField(choices=[('endocrinologist', 'Endocrinologist'), ('diabetes_educator', 'Diabetes Educator'), ('nutritionist', 'Nutritionist'), ('general_practitioner', 'General Practitioner'), ('nurse', 'Nurse'), ('pharmacist', 'Pharmacist'), ('mental_health', 'Mental Health Professional'), ('exercise_physiologist', 'Exercise Physiologist')], max_length=30)),
                ('license_number', models.CharField(blank=True, max_length=50)),
                ('years_experience', models.IntegerField(blank=True, null=True)),
                ('education', models.TextField(blank=True)),
                ('certifications', models.TextField(blank=True)),
                ('bio', models.TextField(blank=True)),
                ('phone_number', models.CharField(blank=True, max_length=15)),
                ('office_address', models.TextField(blank=True)),
                ('consultation_fee', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('consultation_duration', models.IntegerField(default=30, help_text='Default consultation duration in minutes')),
                ('is_accepting_patients', models.BooleanField(default=True)),
                ('is_available_for_emergency', models.BooleanField(default=False)),
                ('languages_spoken', models.CharField(blank=True, help_text='Comma-separated languages', max_length=200)),
                ('average_rating', models.FloatField(default=0.0)),
                ('total_ratings', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ProviderNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note_type', models.CharField(choices=[('consultation', 'Consultation Note'), ('assessment', 'Assessment'), ('treatment_plan', 'Treatment Plan'), ('follow_up', 'Follow-up'), ('medication_review', 'Medication Review'), ('general', 'General Note')], max_length=30)),
                ('title', models.CharField(max_length=200)),
                ('content', models.TextField()),
                ('is_shared_with_patient', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='provider_notes', to='patients.patientprofile')),
                ('provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='clinical_notes', to='providers.providerprofile')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProviderTimeOff',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_datetime', models.DateTimeField()),
                ('end_datetime', models.DateTimeField()),
                ('reason', models.CharField(blank=True, max_length=200)),
                ('is_recurring', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='time_off', to='providers.providerprofile')),
            ],
            options={
                'ordering': ['start_datetime'],
            },
        ),
        migrations.CreateModel(
            name='ProviderPatientAssignment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_primary', models.BooleanField(default=False, help_text="Is this the patient's primary provider?")),
                ('assigned_at', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
                ('notes', models.TextField(blank=True)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='provider_assignments', to='patients.patientprofile')),
                ('provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='patient_assignments', to='providers.providerprofile')),
            ],
            options={
                'unique_together': {('provider', 'patient')},
            },
        ),
        migrations.CreateModel(
            name='ProviderRating',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], help_text='1-5 stars')),
                ('review', models.TextField(blank=True)),
                ('communication_rating', models.IntegerField(blank=True, choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], null=True)),
                ('knowledge_rating', models.IntegerField(blank=True, choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], null=True)),
                ('punctuality_rating', models.IntegerField(blank=True, choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], null=True)),
                ('would_recommend', models.BooleanField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='provider_ratings', to='patients.patientprofile')),
                ('provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ratings', to='providers.providerprofile')),
            ],
            options={
                'ordering': ['-created_at'],
                'unique_together': {('provider', 'patient')},
            },
        ),
        migrations.CreateModel(
            name='ProviderSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('day_of_week', models.IntegerField(choices=[(0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'), (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday')])),
                ('start_time', models.TimeField()),
                ('end_time', models.TimeField()),
                ('is_available', models.BooleanField(default=True)),
                ('break_start', models.TimeField(blank=True, null=True)),
                ('break_end', models.TimeField(blank=True, null=True)),
                ('provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedules', to='providers.providerprofile')),
            ],
            options={
                'ordering': ['day_of_week', 'start_time'],
                'unique_together': {('provider', 'day_of_week')},
            },
        ),
    ]
