# Generated by Django 5.2.3 on 2025-06-30 15:20

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('patients', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Appointment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('appointment_type', models.CharField(choices=[('consultation', 'General Consultation'), ('follow_up', 'Follow-up'), ('education', 'Diabetes Education'), ('nutrition', 'Nutrition Counseling'), ('mental_health', 'Mental Health Support'), ('medication_review', 'Medication Review')], max_length=30)),
                ('scheduled_datetime', models.DateTimeField()),
                ('duration_minutes', models.IntegerField(default=30)),
                ('status', models.CharField(choices=[('scheduled', 'Scheduled'), ('confirmed', 'Confirmed'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('no_show', 'No Show')], default='scheduled', max_length=20)),
                ('chief_complaint', models.TextField(blank=True, help_text="Patient's main concern")),
                ('symptoms', models.TextField(blank=True)),
                ('questions', models.TextField(blank=True)),
                ('diagnosis', models.TextField(blank=True)),
                ('treatment_plan', models.TextField(blank=True)),
                ('follow_up_instructions', models.TextField(blank=True)),
                ('next_appointment_recommended', models.BooleanField(default=False)),
                ('meeting_link', models.URLField(blank=True)),
                ('meeting_id', models.CharField(blank=True, max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='appointments', to='patients.patientprofile')),
            ],
            options={
                'ordering': ['-scheduled_datetime'],
            },
        ),
        migrations.CreateModel(
            name='AppointmentFeedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], help_text='1-5 stars')),
                ('comments', models.TextField(blank=True)),
                ('would_recommend', models.BooleanField(blank=True, null=True)),
                ('technical_issues', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('appointment', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='feedback', to='appointments.appointment')),
            ],
        ),
        migrations.CreateModel(
            name='AppointmentMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.TextField()),
                ('is_read', models.BooleanField(default=False)),
                ('sent_at', models.DateTimeField(auto_now_add=True)),
                ('appointment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='appointments.appointment')),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['sent_at'],
            },
        ),
        migrations.CreateModel(
            name='HealthcareProvider',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('specialization', models.CharField(choices=[('endocrinologist', 'Endocrinologist'), ('diabetes_educator', 'Diabetes Educator'), ('nutritionist', 'Nutritionist'), ('general_practitioner', 'General Practitioner'), ('nurse', 'Nurse'), ('pharmacist', 'Pharmacist'), ('mental_health', 'Mental Health Professional')], max_length=100)),
                ('license_number', models.CharField(blank=True, max_length=50)),
                ('years_experience', models.IntegerField(blank=True, null=True)),
                ('bio', models.TextField(blank=True)),
                ('consultation_fee', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('is_available', models.BooleanField(default=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='appointment',
            name='provider',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='appointments', to='appointments.healthcareprovider'),
        ),
        migrations.CreateModel(
            name='ProviderAvailability',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('day_of_week', models.IntegerField(choices=[(0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'), (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday')])),
                ('start_time', models.TimeField()),
                ('end_time', models.TimeField()),
                ('is_available', models.BooleanField(default=True)),
                ('provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='availability', to='appointments.healthcareprovider')),
            ],
            options={
                'ordering': ['day_of_week', 'start_time'],
                'unique_together': {('provider', 'day_of_week', 'start_time')},
            },
        ),
    ]
