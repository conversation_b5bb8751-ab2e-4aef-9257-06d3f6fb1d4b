{% extends 'base.html' %}
{% load static %}

{% block title %}Fast Register - Telehealth Diabetes Care{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow">
                <div class="card-header bg-success text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        Quick Register
                    </h4>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    
                    <form method="post" id="fastRegisterForm">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="{{ form.first_name }}" required autofocus
                                       placeholder="Enter your first name">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="{{ form.last_name }}" required
                                       placeholder="Enter your last name">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="{{ form.email }}" required
                                   placeholder="Enter your email address">
                        </div>
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" name="username" 
                                   value="{{ form.username }}" required
                                   placeholder="Choose a username">
                            <div class="form-text">Letters, numbers, and @/./+/-/_ only.</div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password1" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password1" name="password1" 
                                       required placeholder="Enter your password">
                                <div class="form-text">Minimum 3 characters for development.</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="password2" class="form-label">Confirm Password</label>
                                <input type="password" class="form-control" id="password2" name="password2" 
                                       required placeholder="Confirm your password">
                            </div>
                        </div>
                        
                        <!-- Loading indicator -->
                        <div id="fastRegisterLoadingIndicator" class="text-center mb-3" style="display: none;">
                            <div class="spinner-border spinner-border-sm text-success" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <span class="ms-2">Creating your account...</span>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-success" id="fastRegisterBtn">
                                <i class="fas fa-user-plus me-2"></i>
                                Register
                            </button>
                        </div>
                    </form>
                    
                    <hr>
                    
                    <div class="text-center">
                        <p class="mb-0">Already have an account?</p>
                        <a href="{% url 'accounts:fast_login' %}" class="btn btn-outline-success">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            Login
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-control {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
}

.form-control:focus {
    border-color: #198754;
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('fastRegisterForm');
    const btn = document.getElementById('fastRegisterBtn');
    const loading = document.getElementById('fastRegisterLoadingIndicator');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            // Quick client-side validation
            const password1 = document.getElementById('password1').value;
            const password2 = document.getElementById('password2').value;
            
            if (password1 !== password2) {
                alert('Passwords do not match!');
                e.preventDefault();
                return;
            }
            
            // Show loading immediately
            if (btn) {
                btn.disabled = true;
                btn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Creating Account...';
            }
            
            if (loading) {
                loading.style.display = 'block';
            }
            
            // Re-enable after timeout (safety)
            setTimeout(function() {
                if (btn) {
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-user-plus me-2"></i>Register';
                }
                if (loading) {
                    loading.style.display = 'none';
                }
            }, 8000);
        });
    }
    
    // Real-time password confirmation
    const password1 = document.getElementById('password1');
    const password2 = document.getElementById('password2');
    
    if (password1 && password2) {
        password2.addEventListener('input', function() {
            if (password1.value !== password2.value) {
                password2.style.borderColor = '#dc3545';
            } else {
                password2.style.borderColor = '#198754';
            }
        });
    }
});
</script>
{% endblock %}
