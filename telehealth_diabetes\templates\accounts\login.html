{% extends 'base.html' %}
{% load static %}

{% block title %}Login - Telehealth Diabetes Care{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        Login
                    </h4>
                </div>
                <div class="card-body">
                    {% if form.errors %}
                        <div class="alert alert-danger">
                            {{ form.errors }}
                        </div>
                    {% endif %}
                    
                    <form method="post" id="loginForm">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">Username</label>
                            {{ form.username }}
                            {% if form.username.errors %}
                                <div class="text-danger small">{{ form.username.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="mb-3">
                            <label for="{{ form.password.id_for_label }}" class="form-label">Password</label>
                            {{ form.password }}
                            {% if form.password.errors %}
                                <div class="text-danger small">{{ form.password.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <!-- Loading indicator -->
                        <div id="loadingIndicator" class="text-center mb-3" style="display: none;">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <span class="ms-2">Signing you in...</span>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary" id="loginBtn">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Login
                            </button>
                        </div>

                        <!-- Quick login for testing -->
                        {% if debug %}
                        <div class="mt-3">
                            <small class="text-muted">Development Mode:</small>
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="quickLogin('admin', 'admin')">
                                    Quick Login (Admin)
                                </button>
                            </div>
                        </div>
                        {% endif %}
                    </form>
                    
                    <hr>
                    
                    <div class="text-center">
                        <div class="mb-3">
                            <a href="{% url 'accounts:forgot_password' %}" class="text-decoration-none">
                                <i class="fas fa-key me-1"></i>
                                Forgot your password?
                            </a>
                        </div>
                        <p class="mb-0">Don't have an account?</p>
                        <a href="{% url 'accounts:register' %}" class="btn btn-outline-primary">
                            <i class="fas fa-user-plus me-2"></i>
                            Register
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-control {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const loginBtn = document.getElementById('loginBtn');
    const loadingIndicator = document.getElementById('loadingIndicator');

    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            // Show loading state
            if (loginBtn) {
                loginBtn.disabled = true;
                loginBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Signing in...';
            }

            if (loadingIndicator) {
                loadingIndicator.style.display = 'block';
            }

            // Set a timeout to re-enable the button if something goes wrong
            setTimeout(function() {
                if (loginBtn) {
                    loginBtn.disabled = false;
                    loginBtn.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>Login';
                }
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'none';
                }
            }, 10000); // 10 seconds timeout
        });
    }

    // Auto-focus on username field
    const usernameField = document.querySelector('input[name="username"]');
    if (usernameField) {
        usernameField.focus();
    }

    // Enter key handling
    const passwordField = document.querySelector('input[name="password"]');
    if (passwordField) {
        passwordField.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                loginForm.submit();
            }
        });
    }
});

// Quick login function for development
function quickLogin(username, password) {
    document.querySelector('input[name="username"]').value = username;
    document.querySelector('input[name="password"]').value = password;
    document.getElementById('loginForm').submit();
}
</script>
{% endblock %}
