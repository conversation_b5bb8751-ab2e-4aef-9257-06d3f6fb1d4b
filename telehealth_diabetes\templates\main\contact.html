{% extends 'base.html' %}
{% load static %}

{% block title %}Contact Us - Telehealth Diabetes Care{% endblock %}

{% block content %}
<div class="container py-5">
    <!-- Hero Section -->
    <div class="row mb-5">
        <div class="col-12 text-center">
            <h1 class="display-4 text-primary mb-4">
                <i class="fas fa-envelope me-3"></i>
                Contact Us
            </h1>
            <p class="lead">We're here to help you with your diabetes care journey</p>
        </div>
    </div>

    <div class="row">
        <!-- Contact Form -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">
                        <i class="fas fa-paper-plane me-2"></i>
                        Send us a Message
                    </h3>
                </div>
                <div class="card-body p-5">
                    <form method="post">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="subject" class="form-label">Subject *</label>
                            <select class="form-select" id="subject" name="subject" required>
                                <option value="">Select a subject...</option>
                                <option value="general">General Inquiry</option>
                                <option value="technical">Technical Support</option>
                                <option value="billing">Billing Question</option>
                                <option value="appointment">Appointment Help</option>
                                <option value="feedback">Feedback</option>
                                <option value="partnership">Partnership Inquiry</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label">Message *</label>
                            <textarea class="form-control" id="message" name="message" rows="5" required 
                                      placeholder="Please describe your question or concern in detail..."></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="newsletter" name="newsletter">
                                <label class="form-check-label" for="newsletter">
                                    Subscribe to our newsletter for diabetes care tips and updates
                                </label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-paper-plane me-2"></i>
                            Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Contact Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6 class="text-success">
                            <i class="fas fa-phone me-2"></i>Phone Support
                        </h6>
                        <p class="mb-1">+254 (0) 123-456-789</p>
                        <small class="text-muted">Available 24/7</small>
                    </div>
                    
                    <div class="mb-4">
                        <h6 class="text-success">
                            <i class="fas fa-envelope me-2"></i>Email Support
                        </h6>
                        <p class="mb-1"><EMAIL></p>
                        <small class="text-muted">Response within 24 hours</small>
                    </div>
                    
                    <div class="mb-4">
                        <h6 class="text-success">
                            <i class="fas fa-map-marker-alt me-2"></i>Office Location
                        </h6>
                        <p class="mb-1">DiabetesCare Center<br>
                        Kisumu, Kenya</p>
                        <small class="text-muted">By appointment only</small>
                    </div>
                    
                    <div class="mb-4">
                        <h6 class="text-success">
                            <i class="fas fa-clock me-2"></i>Business Hours
                        </h6>
                        <p class="mb-1">Monday - Friday: 8:00 AM - 6:00 PM<br>
                        Saturday: 9:00 AM - 2:00 PM<br>
                        Sunday: Emergency only</p>
                    </div>
                </div>
            </div>

            <!-- Emergency Contact -->
            <div class="card border-0 shadow-sm bg-danger text-white">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                    <h5>Medical Emergency?</h5>
                    <p>If you're experiencing a medical emergency, please call emergency services immediately or go to your nearest emergency room.</p>
                    <p class="mb-0"><strong>Emergency: 999</strong></p>
                </div>
            </div>
        </div>
    </div>

    <!-- FAQ Section -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-0 shadow-lg bg-light">
                <div class="card-body p-5">
                    <h2 class="text-center text-primary mb-4">Quick Answers</h2>
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="card border-0 h-100">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">
                                        <i class="fas fa-question-circle me-2"></i>
                                        How do I reset my password?
                                    </h5>
                                    <p class="card-text">Click "Forgot Password" on the login page and follow the instructions sent to your email.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="card border-0 h-100">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">
                                        <i class="fas fa-question-circle me-2"></i>
                                        How do I schedule an appointment?
                                    </h5>
                                    <p class="card-text">Log into your account, go to "Appointments" and click "Schedule New Appointment".</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="card border-0 h-100">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">
                                        <i class="fas fa-question-circle me-2"></i>
                                        Is my data secure?
                                    </h5>
                                    <p class="card-text">Yes, we use bank-level encryption and are fully HIPAA compliant to protect your health information.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="card border-0 h-100">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">
                                        <i class="fas fa-question-circle me-2"></i>
                                        Do you accept insurance?
                                    </h5>
                                    <p class="card-text">Yes, we accept most major insurance plans including Medicare and Medicaid.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="text-center">
                        <a href="{% url 'main:faq' %}" class="btn btn-primary">
                            <i class="fas fa-question-circle me-2"></i>
                            View All FAQs
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Social Media -->
    <div class="row mt-5">
        <div class="col-12 text-center">
            <h3 class="text-primary mb-4">Follow Us</h3>
            <div class="d-flex justify-content-center gap-3">
                <a href="#" class="btn btn-outline-primary btn-lg">
                    <i class="fab fa-facebook-f"></i>
                </a>
                <a href="#" class="btn btn-outline-info btn-lg">
                    <i class="fab fa-twitter"></i>
                </a>
                <a href="#" class="btn btn-outline-danger btn-lg">
                    <i class="fab fa-instagram"></i>
                </a>
                <a href="#" class="btn btn-outline-primary btn-lg">
                    <i class="fab fa-linkedin-in"></i>
                </a>
                <a href="#" class="btn btn-outline-success btn-lg">
                    <i class="fab fa-whatsapp"></i>
                </a>
            </div>
            <p class="mt-3 text-muted">Stay connected for the latest updates and diabetes care tips</p>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('form');
    const requiredFields = form.querySelectorAll('[required]');
    
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });
    
    // Remove validation styling on input
    requiredFields.forEach(field => {
        field.addEventListener('input', function() {
            if (this.value.trim()) {
                this.classList.remove('is-invalid');
            }
        });
    });
});
</script>

<style>
.is-invalid {
    border-color: #dc3545;
}
</style>
{% endblock %}
