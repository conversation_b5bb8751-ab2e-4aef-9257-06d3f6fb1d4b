/* Custom CSS for Telehealth Diabetes Care */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
}

/* Navigation Styles */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: #fff !important;
    transform: translateY(-1px);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    min-height: 60vh;
    display: flex;
    align-items: center;
}

.hero-section h1 {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-section .btn {
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.hero-section .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.3);
}

/* Feature Cards */
.card {
    transition: all 0.3s ease;
    border-radius: 15px;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.feature-icon {
    transition: all 0.3s ease;
}

.card:hover .feature-icon {
    transform: scale(1.1);
}

/* Statistics Section */
.stat-item h3 {
    font-weight: 800;
    margin-bottom: 0.5rem;
}

.stat-item p {
    font-size: 1.1rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Buttons */
.btn {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* Forms */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* Alerts */
.alert {
    border-radius: 10px;
    border: none;
    font-weight: 500;
}

/* Footer */
footer {
    background: linear-gradient(135deg, var(--dark-color) 0%, #343a40 100%);
}

footer h5 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

footer a {
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: var(--primary-color) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        min-height: 50vh;
        text-align: center;
    }
    
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .display-5 {
        font-size: 1.75rem;
    }
}

/* Custom Components */
.dashboard-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.metric-card {
    text-align: center;
    padding: 2rem;
    border-radius: 15px;
    background: white;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.metric-value {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
}

.metric-label {
    color: var(--secondary-color);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
}

/* Loading Spinner */
.spinner-custom {
    width: 3rem;
    height: 3rem;
    border: 0.3rem solid rgba(13, 110, 253, 0.2);
    border-top: 0.3rem solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for better accessibility */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .navbar,
    footer,
    .btn {
        display: none !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
}
