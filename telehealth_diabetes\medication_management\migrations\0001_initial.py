# Generated by Django 5.2.3 on 2025-06-30 15:20

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('patients', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Medication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('generic_name', models.CharField(blank=True, max_length=200)),
                ('medication_type', models.CharField(choices=[('insulin', 'Insulin'), ('metformin', 'Metformin'), ('sulfonylurea', 'Sulfonylurea'), ('dpp4', 'DPP-4 Inhibitor'), ('sglt2', 'SGLT-2 Inhibitor'), ('glp1', 'GLP-1 Agonist'), ('other', 'Other')], max_length=50)),
                ('description', models.TextField(blank=True)),
                ('common_side_effects', models.TextField(blank=True)),
                ('warnings', models.TextField(blank=True)),
            ],
        ),
        migrations.CreateModel(
            name='PatientMedication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dosage', models.CharField(help_text='e.g., 500mg, 10 units', max_length=100)),
                ('frequency', models.CharField(help_text='e.g., twice daily, before meals', max_length=100)),
                ('instructions', models.TextField(blank=True)),
                ('prescribed_by', models.CharField(blank=True, max_length=100)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('medication', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='medication_management.medication')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='medications', to='patients.patientprofile')),
            ],
            options={
                'ordering': ['is_active', 'medication__name'],
            },
        ),
        migrations.CreateModel(
            name='MedicationReminder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reminder_time', models.TimeField()),
                ('is_active', models.BooleanField(default=True)),
                ('days_of_week', models.CharField(default='1234567', help_text='1=Monday, 7=Sunday', max_length=20)),
                ('patient_medication', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reminders', to='medication_management.patientmedication')),
            ],
        ),
        migrations.CreateModel(
            name='MedicationRefillRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pharmacy_name', models.CharField(blank=True, max_length=200)),
                ('pharmacy_phone', models.CharField(blank=True, max_length=15)),
                ('quantity_requested', models.CharField(max_length=100)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('denied', 'Denied'), ('completed', 'Completed')], default='pending', max_length=20)),
                ('notes', models.TextField(blank=True)),
                ('requested_at', models.DateTimeField(auto_now_add=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('processed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_refills', to=settings.AUTH_USER_MODEL)),
                ('patient_medication', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='refill_requests', to='medication_management.patientmedication')),
            ],
            options={
                'ordering': ['-requested_at'],
            },
        ),
        migrations.CreateModel(
            name='MedicationLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('taken_at', models.DateTimeField()),
                ('dosage_taken', models.CharField(blank=True, max_length=100)),
                ('notes', models.TextField(blank=True)),
                ('was_reminder', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('patient_medication', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='logs', to='medication_management.patientmedication')),
            ],
            options={
                'ordering': ['-taken_at'],
            },
        ),
        migrations.CreateModel(
            name='DrugInteraction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('severity', models.CharField(choices=[('minor', 'Minor'), ('moderate', 'Moderate'), ('major', 'Major'), ('severe', 'Severe')], max_length=20)),
                ('description', models.TextField()),
                ('recommendation', models.TextField(blank=True)),
                ('medication1', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interactions_as_med1', to='medication_management.medication')),
                ('medication2', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interactions_as_med2', to='medication_management.medication')),
            ],
            options={
                'unique_together': {('medication1', 'medication2')},
            },
        ),
    ]
