<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Telehealth Diabetes Care{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{% url 'main:home' %}">
                <i class="fas fa-heartbeat me-2"></i>
                DiabetesCare
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'main:home' %}">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'main:about' %}">
                            <i class="fas fa-info-circle me-1"></i>About
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-stethoscope me-1"></i>Telehealth
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'main:how_telehealth_works' %}">How It Works</a></li>
                            <li><a class="dropdown-item" href="{% url 'appointments:schedule' %}">Schedule Appointment</a></li>
                            <li><a class="dropdown-item" href="{% url 'patients:monitoring' %}">Remote Monitoring</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-book-medical me-1"></i>Diabetes Info
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'main:diabetes_info_hub' %}">Diabetes Info Hub</a></li>
                            <li><a class="dropdown-item" href="{% url 'main:tele_education_overview' %}">Tele-Education</a></li>
                            <li><a class="dropdown-item" href="{% url 'education:home' %}">Education Library</a></li>
                            <li><a class="dropdown-item" href="{% url 'education:recipes' %}">Recipes</a></li>
                            <li><a class="dropdown-item" href="{% url 'education:exercises' %}">Exercise Routines</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-users me-1"></i>Community
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'support_groups:list' %}">Support Groups</a></li>
                            <li><a class="dropdown-item" href="{% url 'support_groups:qa_sessions' %}">Live Q&A</a></li>
                            <li><a class="dropdown-item" href="{% url 'mental_health:home' %}">Mental Health</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'main:blog' %}">
                            <i class="fas fa-newspaper me-1"></i>News
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>{{ user.username }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'patients:dashboard' %}"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                                <li><a class="dropdown-item" href="{% url 'patients:profile' %}"><i class="fas fa-user-edit me-2"></i>My Profile</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'health_data:home' %}"><i class="fas fa-chart-line me-2"></i>Health Data</a></li>
                                <li><a class="dropdown-item" href="{% url 'medication_management:list' %}"><i class="fas fa-pills me-2"></i>Medications</a></li>
                                <li><a class="dropdown-item" href="{% url 'appointments:list' %}"><i class="fas fa-calendar-alt me-2"></i>Appointments</a></li>
                                <li><a class="dropdown-item" href="{% url 'goals:list' %}"><i class="fas fa-bullseye me-2"></i>My Goals</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'main:logout' %}"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'main:login' %}">
                                <i class="fas fa-sign-in-alt me-1"></i>Login
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'main:register' %}">
                                <i class="fas fa-user-plus me-1"></i>Register
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid">
        {% if messages %}
            <div class="row mt-3">
                <div class="col-12">
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}
        
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-3">
                    <h5><i class="fas fa-heartbeat me-2"></i>DiabetesCare</h5>
                    <p>Empowering diabetes management from Kisumu – anywhere, anytime.</p>
                </div>
                <div class="col-md-3">
                    <h5>Quick Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="{% url 'main:about' %}" class="text-light">About Us</a></li>
                        <li><a href="{% url 'main:how_telehealth_works' %}" class="text-light">How Telehealth Works</a></li>
                        <li><a href="{% url 'main:diabetes_info_hub' %}" class="text-light">Diabetes Info</a></li>
                        <li><a href="{% url 'main:faq' %}" class="text-light">FAQ</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>Legal & Security</h5>
                    <ul class="list-unstyled">
                        <li><a href="{% url 'main:privacy' %}" class="text-light">Privacy Policy</a></li>
                        <li><a href="{% url 'main:terms' %}" class="text-light">Terms of Service</a></li>
                        <li><a href="{% url 'main:security_tips' %}" class="text-light">Security Tips</a></li>
                        <li><a href="{% url 'main:contact' %}" class="text-light">Contact Us</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>Contact Info</h5>
                    <p><i class="fas fa-phone me-2"></i>+254 (0) 123-456-789</p>
                    <p><i class="fas fa-envelope me-2"></i><EMAIL></p>
                    <p><i class="fas fa-map-marker-alt me-2"></i>Kisumu, Kenya</p>
                    <p><i class="fas fa-clock me-2"></i>24/7 Support Available</p>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; 2025 DiabetesCare. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
