{% extends 'base.html' %}
{% load static %}

{% block title %}Health Data - Telehealth Diabetes Care{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-chart-line me-2 text-primary"></i>
                    My Health Data
                </h1>
                <a href="{% url 'patients:dashboard' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-danger mb-2">
                        <i class="fas fa-tint fa-2x"></i>
                    </div>
                    <h6 class="card-title text-muted">Latest Glucose</h6>
                    {% if recent_glucose %}
                        <h4 class="text-danger mb-1">{{ recent_glucose.value }} mmol/L</h4>
                        <small class="text-muted">{{ recent_glucose.measured_at|timesince }} ago</small>
                    {% else %}
                        <h4 class="text-muted mb-1">No data</h4>
                        <small class="text-muted">Record your first reading</small>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-weight fa-2x"></i>
                    </div>
                    <h6 class="card-title text-muted">Current Weight</h6>
                    {% if recent_weight %}
                        <h4 class="text-success mb-1">{{ recent_weight.weight }} kg</h4>
                        <small class="text-muted">{{ recent_weight.measured_at|timesince }} ago</small>
                    {% else %}
                        <h4 class="text-muted mb-1">No data</h4>
                        <small class="text-muted">Record your weight</small>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-info mb-2">
                        <i class="fas fa-running fa-2x"></i>
                    </div>
                    <h6 class="card-title text-muted">Last Activity</h6>
                    {% if recent_activity %}
                        <h4 class="text-info mb-1">{{ recent_activity.duration_minutes }} min</h4>
                        <small class="text-muted">{{ recent_activity.get_activity_type_display }}</small>
                    {% else %}
                        <h4 class="text-muted mb-1">No data</h4>
                        <small class="text-muted">Log your activity</small>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-moon fa-2x"></i>
                    </div>
                    <h6 class="card-title text-muted">Sleep Quality</h6>
                    <h4 class="text-warning mb-1">Good</h4>
                    <small class="text-muted">Last night</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Entry Forms -->
    <div class="row">
        <div class="col-lg-8">
            <!-- Quick Entry Forms -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-plus-circle me-2 text-primary"></i>
                        Quick Data Entry
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Blood Glucose Form -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-danger mb-3">
                                <i class="fas fa-tint me-2"></i>
                                Blood Glucose
                            </h6>
                            <form method="post" action="{% url 'health_data:glucose' %}">
                                {% csrf_token %}
                                <div class="row">
                                    <div class="col-6">
                                        <input type="number" class="form-control" name="glucose_value" 
                                               placeholder="Value" step="0.1" min="1" max="50" required>
                                    </div>
                                    <div class="col-6">
                                        <select class="form-select" name="measurement_type" required>
                                            <option value="">Type</option>
                                            <option value="fasting">Fasting</option>
                                            <option value="before_meal">Before Meal</option>
                                            <option value="after_meal">After Meal</option>
                                            <option value="bedtime">Bedtime</option>
                                            <option value="random">Random</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <button type="submit" class="btn btn-danger btn-sm">
                                        <i class="fas fa-save me-1"></i>Save Reading
                                    </button>
                                </div>
                            </form>
                        </div>
                        
                        <!-- Weight Form -->
                        <div class="col-md-6">
                            <h6 class="text-success mb-3">
                                <i class="fas fa-weight me-2"></i>
                                Weight
                            </h6>
                            <form method="post" action="{% url 'health_data:weight' %}">
                                {% csrf_token %}
                                <div class="row">
                                    <div class="col-8">
                                        <input type="number" class="form-control" name="weight" 
                                               placeholder="Weight (kg)" step="0.1" min="20" max="500" required>
                                    </div>
                                    <div class="col-4">
                                        <button type="submit" class="btn btn-success btn-sm w-100">
                                            <i class="fas fa-save me-1"></i>Save
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Activity Form -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-info mb-3">
                                <i class="fas fa-running me-2"></i>
                                Physical Activity
                            </h6>
                            <form method="post" action="{% url 'health_data:activity' %}">
                                {% csrf_token %}
                                <div class="row">
                                    <div class="col-md-4">
                                        <select class="form-select" name="activity_type" required>
                                            <option value="">Activity Type</option>
                                            <option value="walking">Walking</option>
                                            <option value="running">Running</option>
                                            <option value="cycling">Cycling</option>
                                            <option value="swimming">Swimming</option>
                                            <option value="gym">Gym Workout</option>
                                            <option value="sports">Sports</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="number" class="form-control" name="duration" 
                                               placeholder="Minutes" min="1" max="600" required>
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select" name="intensity" required>
                                            <option value="">Intensity</option>
                                            <option value="low">Low</option>
                                            <option value="moderate">Moderate</option>
                                            <option value="high">High</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="submit" class="btn btn-info btn-sm w-100">
                                            <i class="fas fa-save me-1"></i>Log
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Entries -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2 text-secondary"></i>
                        Recent Entries
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Type</th>
                                    <th>Value</th>
                                    <th>Date/Time</th>
                                    <th>Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="badge bg-danger">Glucose</span></td>
                                    <td>7.2 mmol/L</td>
                                    <td>Today, 8:30 AM</td>
                                    <td>Before breakfast</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-success">Weight</span></td>
                                    <td>72.5 kg</td>
                                    <td>Today, 7:00 AM</td>
                                    <td>Morning weigh-in</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-info">Activity</span></td>
                                    <td>30 minutes</td>
                                    <td>Yesterday, 6:00 PM</td>
                                    <td>Evening walk</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2 text-warning"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'health_data:glucose' %}" class="btn btn-outline-danger">
                            <i class="fas fa-tint me-2"></i>Detailed Glucose Entry
                        </a>
                        <a href="{% url 'health_data:weight' %}" class="btn btn-outline-success">
                            <i class="fas fa-weight me-2"></i>Weight Tracking
                        </a>
                        <a href="{% url 'health_data:activity' %}" class="btn btn-outline-info">
                            <i class="fas fa-running me-2"></i>Activity Log
                        </a>
                        <a href="{% url 'health_data:reports' %}" class="btn btn-outline-primary">
                            <i class="fas fa-chart-bar me-2"></i>View Reports
                        </a>
                    </div>
                </div>
            </div>

            <!-- Health Tips -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb me-2 text-success"></i>
                        Today's Health Tip
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-apple-alt fa-3x text-success"></i>
                    </div>
                    <h6 class="text-center mb-2">Monitor Your Patterns</h6>
                    <p class="text-muted text-center mb-3">
                        Track your blood glucose at the same times each day to identify patterns 
                        and better understand how food, activity, and stress affect your levels.
                    </p>
                    <div class="text-center">
                        <a href="{% url 'education:home' %}" class="btn btn-sm btn-outline-success">
                            More Tips
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
