from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils import timezone
from .models import Appointment, HealthcareProvider

@login_required
def schedule(request):
    """Schedule new appointment"""
    providers = HealthcareProvider.objects.filter(is_available=True)

    if request.method == 'POST':
        # Handle appointment scheduling
        messages.info(request, 'Appointment scheduling functionality will be implemented.')
        return redirect('appointments:list')

    return render(request, 'appointments/schedule.html', {
        'providers': providers
    })

@login_required
def appointment_list(request):
    """List patient appointments"""
    try:
        patient_profile = request.user.patientprofile
        appointments = patient_profile.appointments.order_by('-scheduled_datetime')
    except:
        appointments = []
        messages.info(request, 'Please complete your profile first.')

    return render(request, 'appointments/list.html', {
        'appointments': appointments
    })

@login_required
def appointment_detail(request, appointment_id):
    """Appointment detail view"""
    try:
        patient_profile = request.user.patientprofile
        appointment = get_object_or_404(Appointment, id=appointment_id, patient=patient_profile)
    except:
        messages.error(request, 'Appointment not found.')
        return redirect('appointments:list')

    return render(request, 'appointments/detail.html', {
        'appointment': appointment
    })
