{% extends 'base.html' %}
{% load static %}

{% block title %}Register - Telehealth Diabetes Care{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-header bg-success text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        Register
                    </h4>
                </div>
                <div class="card-body">
                    {% if form.errors %}
                        <div class="alert alert-danger">
                            {{ form.errors }}
                        </div>
                    {% endif %}
                    
                    <form method="post" id="registerForm">
                        {% csrf_token %}

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">First Name</label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger small">{{ form.first_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">Last Name</label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger small">{{ form.last_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">Email Address</label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="text-danger small">{{ form.email.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">Username</label>
                            {{ form.username }}
                            {% if form.username.errors %}
                                <div class="text-danger small">{{ form.username.errors.0 }}</div>
                            {% endif %}
                            {% if form.username.help_text %}
                                <div class="form-text">{{ form.username.help_text }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.password1.id_for_label }}" class="form-label">Password</label>
                            {{ form.password1 }}
                            {% if form.password1.errors %}
                                <div class="text-danger small">{{ form.password1.errors.0 }}</div>
                            {% endif %}
                            {% if form.password1.help_text %}
                                <div class="form-text">{{ form.password1.help_text }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.password2.id_for_label }}" class="form-label">Confirm Password</label>
                            {{ form.password2 }}
                            {% if form.password2.errors %}
                                <div class="text-danger small">{{ form.password2.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <!-- Loading indicator -->
                        <div id="registerLoadingIndicator" class="text-center mb-3" style="display: none;">
                            <div class="spinner-border spinner-border-sm text-success" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <span class="ms-2">Creating your account...</span>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-success" id="registerBtn">
                                <i class="fas fa-user-plus me-2"></i>
                                Register
                            </button>
                        </div>
                    </form>
                    
                    <hr>
                    
                    <div class="text-center">
                        <p class="mb-0">Already have an account?</p>
                        <a href="{% url 'accounts:login' %}" class="btn btn-outline-success">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            Login
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-control {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
}

.form-control:focus {
    border-color: #198754;
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.password-strength {
    height: 5px;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.password-weak { background-color: #dc3545; }
.password-medium { background-color: #ffc107; }
.password-strong { background-color: #198754; }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const registerForm = document.getElementById('registerForm');
    const registerBtn = document.getElementById('registerBtn');
    const loadingIndicator = document.getElementById('registerLoadingIndicator');

    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            // Show loading state
            if (registerBtn) {
                registerBtn.disabled = true;
                registerBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Creating Account...';
            }

            if (loadingIndicator) {
                loadingIndicator.style.display = 'block';
            }

            // Set a timeout to re-enable the button if something goes wrong
            setTimeout(function() {
                if (registerBtn) {
                    registerBtn.disabled = false;
                    registerBtn.innerHTML = '<i class="fas fa-user-plus me-2"></i>Register';
                }
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'none';
                }
            }, 15000); // 15 seconds timeout for registration
        });
    }

    // Auto-focus on first name field
    const firstNameField = document.querySelector('input[name="first_name"]');
    if (firstNameField) {
        firstNameField.focus();
    }

    // Password strength indicator
    const password1Field = document.querySelector('input[name="password1"]');
    if (password1Field) {
        // Create password strength indicator
        const strengthIndicator = document.createElement('div');
        strengthIndicator.className = 'password-strength mt-1';
        strengthIndicator.style.width = '0%';
        password1Field.parentNode.appendChild(strengthIndicator);

        password1Field.addEventListener('input', function() {
            const password = this.value;
            let strength = 0;

            if (password.length >= 6) strength += 25;
            if (password.match(/[a-z]/)) strength += 25;
            if (password.match(/[A-Z]/)) strength += 25;
            if (password.match(/[0-9]/)) strength += 25;

            strengthIndicator.style.width = strength + '%';

            if (strength < 50) {
                strengthIndicator.className = 'password-strength mt-1 password-weak';
            } else if (strength < 75) {
                strengthIndicator.className = 'password-strength mt-1 password-medium';
            } else {
                strengthIndicator.className = 'password-strength mt-1 password-strong';
            }
        });
    }

    // Real-time password confirmation validation
    const password2Field = document.querySelector('input[name="password2"]');
    if (password1Field && password2Field) {
        password2Field.addEventListener('input', function() {
            if (password1Field.value !== password2Field.value) {
                password2Field.setCustomValidity('Passwords do not match');
                password2Field.classList.add('is-invalid');
            } else {
                password2Field.setCustomValidity('');
                password2Field.classList.remove('is-invalid');
                password2Field.classList.add('is-valid');
            }
        });
    }
});
</script>
{% endblock %}
