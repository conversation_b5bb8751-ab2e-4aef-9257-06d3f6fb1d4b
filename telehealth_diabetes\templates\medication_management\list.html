{% extends 'base.html' %}
{% load static %}

{% block title %}My Medications - Telehealth Diabetes Care{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-pills me-2 text-primary"></i>
                    My Medications
                </h1>
                <div>
                    <a href="{% url 'patients:dashboard' %}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>Dashboard
                    </a>
                    <a href="{% url 'medication_management:add' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Add Medication
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-pills fa-2x text-primary mb-2"></i>
                    <h6 class="card-title">Active Medications</h6>
                    <h4 class="text-primary mb-0">{{ active_medications|length }}</h4>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                    <h6 class="card-title">Pending Refills</h6>
                    <h4 class="text-warning mb-0">{{ pending_refills|length }}</h4>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <a href="{% url 'medication_management:log' %}" class="text-decoration-none">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <h6 class="card-title">Log Medication</h6>
                        <small class="text-muted">Record taken dose</small>
                    </div>
                </div>
            </a>
        </div>
        <div class="col-md-3 mb-3">
            <a href="{% url 'medication_management:interactions' %}" class="text-decoration-none">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                        <h6 class="card-title">Check Interactions</h6>
                        <small class="text-muted">Drug safety check</small>
                    </div>
                </div>
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Active Medications -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-pills me-2 text-primary"></i>
                        Active Medications
                    </h5>
                    <a href="{% url 'medication_management:add' %}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-plus me-1"></i>Add New
                    </a>
                </div>
                <div class="card-body">
                    {% if active_medications %}
                        {% for medication in active_medications %}
                            <div class="card mb-3 border-start border-primary border-3">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-md-6">
                                            <h6 class="card-title mb-1">{{ medication.medication.name }}</h6>
                                            <p class="text-muted mb-1">
                                                <strong>Dosage:</strong> {{ medication.dosage }}<br>
                                                <strong>Frequency:</strong> {{ medication.frequency }}
                                            </p>
                                            {% if medication.instructions %}
                                                <small class="text-muted">{{ medication.instructions }}</small>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-3">
                                            <small class="text-muted">
                                                <strong>Started:</strong> {{ medication.start_date|date:"M d, Y" }}<br>
                                                {% if medication.prescribed_by %}
                                                    <strong>Prescribed by:</strong> {{ medication.prescribed_by }}
                                                {% endif %}
                                            </small>
                                        </div>
                                        <div class="col-md-3 text-end">
                                            <div class="btn-group-vertical btn-group-sm" role="group">
                                                <button type="button" class="btn btn-success" onclick="quickLog({{ medication.id }}, '{{ medication.medication.name }}')">
                                                    <i class="fas fa-check me-1"></i>Log Taken
                                                </button>
                                                <a href="{% url 'medication_management:detail' medication.id %}" class="btn btn-outline-primary">
                                                    <i class="fas fa-eye me-1"></i>Details
                                                </a>
                                                <a href="{% url 'medication_management:refills' %}?medication={{ medication.id }}" class="btn btn-outline-warning">
                                                    <i class="fas fa-prescription-bottle me-1"></i>Refill
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-pills fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No medications added yet</h5>
                            <p class="text-muted">Start by adding your current medications to track them effectively.</p>
                            <a href="{% url 'medication_management:add' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>Add Your First Medication
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Recent Medication Logs -->
            {% if recent_logs %}
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2 text-secondary"></i>
                            Recent Medication Logs
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Medication</th>
                                        <th>Dosage</th>
                                        <th>Taken At</th>
                                        <th>Notes</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for log in recent_logs %}
                                        <tr>
                                            <td>{{ log.patient_medication.medication.name }}</td>
                                            <td>{{ log.dosage_taken }}</td>
                                            <td>{{ log.taken_at|date:"M d, g:i A" }}</td>
                                            <td>{{ log.notes|default:"-" }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center">
                            <a href="{% url 'medication_management:log' %}" class="btn btn-sm btn-outline-secondary">
                                View All Logs
                            </a>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Pending Refill Requests -->
            {% if pending_refills %}
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-clock me-2"></i>
                            Pending Refills
                        </h5>
                    </div>
                    <div class="card-body">
                        {% for refill in pending_refills %}
                            <div class="d-flex align-items-center mb-3">
                                <div class="me-3">
                                    <i class="fas fa-prescription-bottle text-warning"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-0">{{ refill.patient_medication.medication.name }}</h6>
                                    <small class="text-muted">
                                        Requested {{ refill.requested_at|timesince }} ago
                                    </small>
                                </div>
                                <div>
                                    <span class="badge bg-warning">{{ refill.status|title }}</span>
                                </div>
                            </div>
                        {% endfor %}
                        <div class="text-center">
                            <a href="{% url 'medication_management:refills' %}" class="btn btn-sm btn-outline-warning">
                                View All Requests
                            </a>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Quick Actions -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2 text-success"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'medication_management:log' %}" class="btn btn-outline-success">
                            <i class="fas fa-check-circle me-2"></i>Log Medication
                        </a>
                        <a href="{% url 'medication_management:reminders' %}" class="btn btn-outline-info">
                            <i class="fas fa-bell me-2"></i>Manage Reminders
                        </a>
                        <a href="{% url 'medication_management:refills' %}" class="btn btn-outline-warning">
                            <i class="fas fa-prescription-bottle me-2"></i>Request Refill
                        </a>
                        <a href="{% url 'medication_management:interactions' %}" class="btn btn-outline-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>Check Interactions
                        </a>
                    </div>
                </div>
            </div>

            <!-- Medication Tips -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb me-2 text-info"></i>
                        Medication Tips
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="mb-1">Take as Prescribed</h6>
                        <small class="text-muted">Always follow your doctor's instructions for dosage and timing.</small>
                    </div>
                    <div class="mb-3">
                        <h6 class="mb-1">Set Reminders</h6>
                        <small class="text-muted">Use our reminder system to never miss a dose.</small>
                    </div>
                    <div class="mb-3">
                        <h6 class="mb-1">Monitor Side Effects</h6>
                        <small class="text-muted">Report any unusual symptoms to your healthcare provider.</small>
                    </div>
                    <div class="text-center">
                        <a href="{% url 'education:home' %}" class="btn btn-sm btn-outline-info">
                            More Medication Tips
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Log Modal -->
<div class="modal fade" id="quickLogModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Log Medication</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to log <strong id="medicationName"></strong> as taken?</p>
                <small class="text-muted">This will record the medication as taken at the current time.</small>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="confirmLog">Log Medication</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentMedicationId = null;

function quickLog(medicationId, medicationName) {
    currentMedicationId = medicationId;
    document.getElementById('medicationName').textContent = medicationName;
    new bootstrap.Modal(document.getElementById('quickLogModal')).show();
}

document.getElementById('confirmLog').addEventListener('click', function() {
    if (currentMedicationId) {
        fetch('{% url "medication_management:quick_log" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: `medication_id=${currentMedicationId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error logging medication');
        });
    }
});
</script>
{% endblock %}
